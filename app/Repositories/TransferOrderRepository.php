<?php

namespace App\Repositories;

use App\Contracts\TransferOrderInterface;
use App\Models\TransferOrder;
use App\Models\TransferOrderItem;
use App\Models\TransferRequest;
use App\Models\Warehouse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TransferOrderRepository extends BaseRepository implements TransferOrderInterface
{
    /**
     * TransferOrderRepository constructor.
     */
    public function __construct(TransferOrder $model)
    {
        parent::__construct($model);
    }

    /**
     * Get all transfer orders with pagination
     */
    public function getAllWithPagination($perPage = 15)
    {
        return $this->model->with(['transferRequest', 'fromWarehouse', 'toWarehouse', 'transitWarehouse', 'createdBy', 'approvedBy'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Get transfer order by ID
     */
    public function getById($id)
    {
        return $this->model->with(['transferRequest', 'fromWarehouse', 'toWarehouse', 'transitWarehouse', 'createdBy', 'approvedBy', 'items.product'])
            ->findOrFail($id);
    }

    /**
     * Create new transfer order
     */
    public function create(array $data)
    {
        try {
            DB::beginTransaction();

            // Generate code if not provided
            if (!isset($data['code'])) {
                $data['code'] = TransferOrder::generateCode();
            }

            // Get transit warehouse if not provided
            if (!isset($data['transit_warehouse_id'])) {
                $transitWarehouse = Warehouse::where('is_transit', true)->first();
                if ($transitWarehouse) {
                    $data['transit_warehouse_id'] = $transitWarehouse->id;
                }
            }

            $transferOrder = $this->model->create($data);

            // Create items if provided
            if (isset($data['items']) && is_array($data['items'])) {
                $this->createItems($transferOrder->id, $data['items']);
            }

            DB::commit();
            return $transferOrder;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating transfer order: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update transfer order
     */
    public function update($id, array $data)
    {
        try {
            DB::beginTransaction();

            $transferOrder = $this->getById($id);
            $transferOrder->update($data);

            // Update items if provided
            if (isset($data['items']) && is_array($data['items'])) {
                $this->updateItems($transferOrder->id, $data['items']);
            }

            DB::commit();
            return $transferOrder;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating transfer order: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete transfer order
     */
    public function delete($id)
    {
        try {
            DB::beginTransaction();

            $transferOrder = $this->getById($id);

            // Delete items first
            $transferOrder->items()->delete();

            // Delete transfer order
            $transferOrder->delete();

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting transfer order: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get transfer orders for datatable
     */
    public function getForDatatable(array $params)
    {
        try {
            $query = DB::table('transfer_orders as to')
                ->leftJoin('transfer_requests as tr', 'to.transfer_request_id', '=', 'tr.id')
                ->leftJoin('warehouses as wf', 'to.from_warehouse_id', '=', 'wf.id')
                ->leftJoin('warehouses as wt', 'to.to_warehouse_id', '=', 'wt.id')
                ->leftJoin('warehouses as wtr', 'to.transit_warehouse_id', '=', 'wtr.id')
                ->leftJoin('users as uc', 'to.created_by', '=', 'uc.id')
                ->leftJoin('users as ua', 'to.approved_by', '=', 'ua.id')
                ->select(
                    'to.id',
                    'to.code',
                    'to.status',
                    'to.notes',
                    'to.created_at',
                    'to.approved_at',
                    'tr.code as transfer_request_code',
                    'wf.name as from_warehouse_name',
                    'wt.name as to_warehouse_name',
                    'wtr.name as transit_warehouse_name',
                    'uc.name as created_by_name',
                    'ua.name as approved_by_name'
                )
                ->whereNull('to.deleted_at');

            // Apply filters
            if (isset($params['status']) && !empty($params['status'])) {
                $query->where('to.status', $params['status']);
            }

            if (isset($params['from_warehouse_id']) && !empty($params['from_warehouse_id'])) {
                $query->where('to.from_warehouse_id', $params['from_warehouse_id']);
            }

            if (isset($params['to_warehouse_id']) && !empty($params['to_warehouse_id'])) {
                $query->where('to.to_warehouse_id', $params['to_warehouse_id']);
            }

            if (isset($params['search']['value']) && !empty($params['search']['value'])) {
                $searchValue = $params['search']['value'];
                $query->where(function ($q) use ($searchValue) {
                    $q->where('to.code', 'like', "%{$searchValue}%")
                      ->orWhere('tr.code', 'like', "%{$searchValue}%")
                      ->orWhere('wf.name', 'like', "%{$searchValue}%")
                      ->orWhere('wt.name', 'like', "%{$searchValue}%")
                      ->orWhere('uc.name', 'like', "%{$searchValue}%");
                });
            }

            // Get total count before pagination
            $totalRecords = $query->count();

            // Apply ordering
            if (isset($params['order'][0]['column']) && isset($params['columns'][$params['order'][0]['column']]['data'])) {
                $orderColumn = $params['columns'][$params['order'][0]['column']]['data'];
                $orderDirection = $params['order'][0]['dir'] ?? 'asc';

                $orderColumnMap = [
                    'code' => 'to.code',
                    'transfer_request_code' => 'tr.code',
                    'from_warehouse' => 'wf.name',
                    'to_warehouse' => 'wt.name',
                    'status' => 'to.status',
                    'created_at' => 'to.created_at',
                    'created_by' => 'uc.name'
                ];

                if (isset($orderColumnMap[$orderColumn])) {
                    $query->orderBy($orderColumnMap[$orderColumn], $orderDirection);
                }
            } else {
                $query->orderBy('to.created_at', 'desc');
            }

            // Apply pagination
            if (isset($params['start']) && isset($params['length'])) {
                $query->offset($params['start'])->limit($params['length']);
            }

            $data = $query->get();

            return [
                'data' => $data,
                'recordsTotal' => $totalRecords,
                'recordsFiltered' => $totalRecords
            ];
        } catch (\Exception $e) {
            Log::error('Error getting transfer orders for datatable: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Submit transfer order for approval
     */
    public function submitForApproval($id)
    {
        $transferOrder = $this->getById($id);

        if (!$transferOrder->canBeSubmitted()) {
            throw new \Exception('Phiếu chuyển hàng không thể gửi duyệt.');
        }

        $transferOrder->update(['status' => TransferOrder::STATUS_PENDING]);
        return $transferOrder;
    }

    /**
     * Approve transfer order
     */
    public function approve($id, $approvedBy)
    {
        try {
            DB::beginTransaction();

            $transferOrder = $this->getById($id);

            if (!$transferOrder->canBeApproved()) {
                throw new \Exception('Phiếu chuyển hàng không thể duyệt.');
            }

            // Update transfer order status
            $transferOrder->update([
                'status' => TransferOrder::STATUS_APPROVED,
                'approved_by' => $approvedBy,
                'approved_at' => now()
            ]);

            // Process inventory transactions
            $this->processInventoryTransactions($transferOrder);

            DB::commit();
            return $transferOrder;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error approving transfer order: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Process inventory transactions when transfer order is approved
     */
    private function processInventoryTransactions($transferOrder)
    {
        // This will be implemented to handle inventory movements
        // For now, we'll just log the action
        Log::info('Processing inventory transactions for transfer order: ' . $transferOrder->code);

        // TODO: Implement inventory transaction logic
        // - Decrease inventory in source warehouse
        // - Increase inventory in transit warehouse
    }

    /**
     * Reject transfer order
     */
    public function reject($id, $rejectedBy, $reason = null)
    {
        $transferOrder = $this->getById($id);

        if (!$transferOrder->canBeRejected()) {
            throw new \Exception('Phiếu chuyển hàng không thể từ chối.');
        }

        $updateData = [
            'status' => TransferOrder::STATUS_REJECTED,
            'approved_by' => $rejectedBy,
            'approved_at' => now()
        ];

        if ($reason) {
            $updateData['notes'] = ($transferOrder->notes ? $transferOrder->notes . "\n\n" : '') . "Lý do từ chối: " . $reason;
        }

        $transferOrder->update($updateData);
        return $transferOrder;
    }

    /**
     * Cancel transfer order
     */
    public function cancel($id)
    {
        $transferOrder = $this->getById($id);

        if (!$transferOrder->canBeCancelled()) {
            throw new \Exception('Phiếu chuyển hàng không thể hủy.');
        }

        $transferOrder->update(['status' => TransferOrder::STATUS_CANCELLED]);
        return $transferOrder;
    }

    /**
     * Get approved transfer orders that don't have transfer receipts
     */
    public function getApprovedWithoutTransferReceipt()
    {
        return $this->model->with(['fromWarehouse', 'toWarehouse', 'transitWarehouse'])
            ->where('status', TransferOrder::STATUS_APPROVED)
            ->whereDoesntHave('transferReceipt')
            ->orderBy('approved_at', 'asc')
            ->get();
    }

    /**
     * Create transfer order items
     */
    public function createItems($transferOrderId, array $items)
    {
        try {
            foreach ($items as $item) {
                $item['transfer_order_id'] = $transferOrderId;
                TransferOrderItem::create($item);
            }
            return true;
        } catch (\Exception $e) {
            Log::error('Error creating transfer order items: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update transfer order items
     */
    public function updateItems($transferOrderId, array $items)
    {
        try {
            // Delete existing items
            TransferOrderItem::where('transfer_order_id', $transferOrderId)->delete();

            // Create new items
            $this->createItems($transferOrderId, $items);

            return true;
        } catch (\Exception $e) {
            Log::error('Error updating transfer order items: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete transfer order items
     */
    public function deleteItems($transferOrderId, array $itemIds = [])
    {
        try {
            $query = TransferOrderItem::where('transfer_order_id', $transferOrderId);

            if (!empty($itemIds)) {
                $query->whereIn('id', $itemIds);
            }

            $query->delete();
            return true;
        } catch (\Exception $e) {
            Log::error('Error deleting transfer order items: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create transfer order from transfer request
     */
    public function createFromTransferRequest($transferRequestId, array $data)
    {
        try {
            DB::beginTransaction();

            $transferRequest = TransferRequest::with('items')->findOrFail($transferRequestId);

            // Prepare transfer order data
            $transferOrderData = [
                'transfer_request_id' => $transferRequestId,
                'from_warehouse_id' => $transferRequest->from_warehouse_id,
                'to_warehouse_id' => $transferRequest->to_warehouse_id,
                'notes' => $data['notes'] ?? $transferRequest->notes,
                'created_by' => $data['created_by'],
            ];

            // Get transit warehouse
            $transitWarehouse = Warehouse::where('is_transit', true)->first();
            if ($transitWarehouse) {
                $transferOrderData['transit_warehouse_id'] = $transitWarehouse->id;
            }

            $transferOrder = $this->create($transferOrderData);

            // Copy items from transfer request
            $items = [];
            foreach ($transferRequest->items as $requestItem) {
                $items[] = [
                    'product_id' => $requestItem->product_id,
                    'quantity' => $requestItem->quantity,
                    'notes' => $requestItem->notes,
                ];
            }

            if (!empty($items)) {
                $this->createItems($transferOrder->id, $items);
            }

            DB::commit();
            return $transferOrder;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating transfer order from transfer request: ' . $e->getMessage());
            throw $e;
        }
    }
}
