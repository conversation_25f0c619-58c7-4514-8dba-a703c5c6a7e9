<?php

namespace App\Repositories;

use App\Contracts\WarehouseInterface;
use App\Models\Warehouse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WarehouseRepository extends BaseRepository implements WarehouseInterface
{
    protected $model;

    public function __construct(Warehouse $warehouse)
    {
        $this->model = $warehouse;
    }

    /**
     * Get all warehouses
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllWarehouses()
    {
        return $this->model->all();
    }

    /**
     * Get warehouse by ID
     *
     * @param int $id
     * @return \App\Models\Warehouse|null
     */
    public function getWarehouseById($id)
    {
        return $this->model->find($id);
    }

    /**
     * Create a new warehouse
     *
     * @param array $data
     * @return \App\Models\Warehouse
     */
    public function createWarehouse(array $data)
    {
        try {
            DB::beginTransaction();

            // Create warehouse
            $warehouse = $this->model->create([
                'name' => $data['name'],
                'code' => $data['code'],
                'address' => $data['address'] ?? null,
                'phone' => $data['phone'] ?? null,
                'email' => $data['email'] ?? null,
                'description' => $data['description'] ?? null,
                'is_active' => $data['is_active'] ?? true,
                'is_default' => $data['is_default'] ?? false,
                'is_transit' => $data['is_transit'] ?? false,
            ]);

            // Create default warehouse area
            $warehouse->areas()->create([
                'name' => 'Khu vực mặc định',
                'code' => 'DEFAULT',
                'description' => 'Khu vực mặc định được tạo tự động',
                'is_active' => true,
                'created_by' => auth()->id(),
            ]);

            DB::commit();
            return [
                'success' => true,
                'message' => 'Kho hàng đã được tạo thành công',
                'data' => $warehouse
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating warehouse: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tạo kho hàng: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Update a warehouse
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateWarehouse($id, array $data)
    {
        try {
            $warehouse = $this->model->findOrFail($id);

            $updated = $warehouse->update([
                'name' => $data['name'],
                'code' => $data['code'],
                'address' => $data['address'] ?? $warehouse->address,
                'phone' => $data['phone'] ?? $warehouse->phone,
                'email' => $data['email'] ?? $warehouse->email,
                'description' => $data['description'] ?? $warehouse->description,
                'is_active' => $data['is_active'] ?? $warehouse->is_active,
                'is_default' => $data['is_default'] ?? $warehouse->is_default,
                'is_transit' => $data['is_transit'] ?? $warehouse->is_transit,
            ]);

            return [
                'success' => true,
                'message' => 'Kho hàng đã được cập nhật thành công',
                'data' => $warehouse
            ];
        } catch (\Exception $e) {
            Log::error('Error updating warehouse: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật kho hàng: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Delete a warehouse
     *
     * @param int $id
     * @return bool
     */
    public function deleteWarehouse($id)
    {
        try {
            $warehouse = $this->model->findOrFail($id);

            // Check if warehouse has inventory
            if ($warehouse->inventoryItems()->count() > 0) {
                throw new \Exception('Không thể xóa kho hàng đang có hàng tồn kho');
            }

            // Delete warehouse areas
            $warehouse->areas()->delete();

            // Delete warehouse
            $deleted = $warehouse->delete();

            return [
                'success' => true,
                'message' => 'Kho hàng đã được xóa thành công'
            ];
        } catch (\Exception $e) {
            Log::error('Error deleting warehouse: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Set a warehouse as default
     *
     * @param int $id
     * @return array
     */
    public function setDefaultWarehouse($id)
    {
        try {
            $warehouse = $this->model->findOrFail($id);

            // Reset all warehouses to non-default
            $this->model->where('is_default', true)->update(['is_default' => false]);

            // Set the selected warehouse as default
            $warehouse->update(['is_default' => true]);

            return [
                'success' => true,
                'message' => 'Kho hàng đã được đặt làm mặc định'
            ];
        } catch (\Exception $e) {
            Log::error('Error setting default warehouse: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi đặt kho hàng mặc định: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get warehouses for datatable
     *
     * @param Request $request
     * @return array
     */
    public function getWarehousesForDatatable(Request $request)
    {
        try {
            $query = $this->model->query();

            // Search
            if ($request->has('search') && !empty($request->search['value'])) {
                $searchValue = $request->search['value'];
                $query->where(function ($q) use ($searchValue) {
                    $q->where('name', 'like', "%{$searchValue}%")
                        ->orWhere('code', 'like', "%{$searchValue}%")
                        ->orWhere('address', 'like', "%{$searchValue}%")
                        ->orWhere('phone', 'like', "%{$searchValue}%")
                        ->orWhere('email', 'like', "%{$searchValue}%");
                });
            }

            // Filter by status
            if ($request->has('status') && $request->status !== 'all') {
                $query->where('is_active', $request->status === 'active');
            }

            $recordsTotal = $this->model->count();
            $recordsFiltered = $query->count();

            // Order
            if ($request->has('order') && !empty($request->order)) {
                $order = $request->order[0];
                $columnIndex = $order['column'];
                $columnName = $request->columns[$columnIndex]['data'];
                $columnDirection = $order['dir'];

                if ($columnName !== 'actions') {
                    $query->orderBy($columnName, $columnDirection);
                }
            } else {
                $query->orderBy('name', 'asc');
            }

            // Pagination
            $start = $request->start ?? 0;
            $length = $request->length ?? 10;

            if ($length != -1) {
                $query->skip($start)->take($length);
            }

            $warehouses = $query->get();

            $data = [];
            foreach ($warehouses as $warehouse) {
                // Tạo thông tin liên hệ kết hợp từ phone và email
                $contactInfo = '';
                if ($warehouse->phone) {
                    $contactInfo .= '<div><i class="ri-phone-line me-1"></i>' . $warehouse->phone . '</div>';
                }
                if ($warehouse->email) {
                    $contactInfo .= '<div><i class="ri-mail-line me-1"></i>' . $warehouse->email . '</div>';
                }
                if (empty($contactInfo)) {
                    $contactInfo = '<span class="text-muted">Chưa cập nhật</span>';
                }

                // Tạo trạng thái với badge
                $statusBadge = $warehouse->is_active
                    ? '<span class="badge bg-label-success">Hoạt động</span>'
                    : '<span class="badge bg-label-secondary">Không hoạt động</span>';

                // Tạo trạng thái mặc định với badge
                $defaultBadge = $warehouse->is_default
                    ? '<span class="badge bg-label-primary">Mặc định</span>'
                    : '<button class="btn btn-sm btn-outline-primary btn-set-default" data-id="' . $warehouse->id . '">Thiết lập mặc định</button>';

                $data[] = [
                    'id' => $warehouse->id,
                    'name' => $warehouse->name,
                    'code' => $warehouse->code,
                    'address' => $warehouse->address,
                    'phone' => $warehouse->phone,
                    'email' => $warehouse->email,
                    'contact' => $contactInfo,
                    'is_active' => $statusBadge,
                    'is_default' => $defaultBadge,
                    'created_at' => $warehouse->created_at->format('d/m/Y H:i:s'),
                    'actions' => '' // Will be rendered on the client side
                ];
            }

            return [
                'draw' => intval($request->draw),
                'recordsTotal' => $recordsTotal,
                'recordsFiltered' => $recordsFiltered,
                'data' => $data
            ];
        } catch (\Exception $e) {
            Log::error('Error getting warehouses for datatable: ' . $e->getMessage());

            return [
                'draw' => intval($request->draw),
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'data' => []
            ];
        }
    }

    /**
     * Get warehouse areas
     *
     * @param int $warehouseId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getWarehouseAreas($warehouseId)
    {
        try {
            $warehouse = $this->model->findOrFail($warehouseId);
            return $warehouse->areas;
        } catch (\Exception $e) {
            Log::error('Error getting warehouse areas: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create a warehouse area
     *
     * @param int $warehouseId
     * @param array $data
     * @return \App\Models\WarehouseArea
     */
    public function createWarehouseArea($warehouseId, array $data)
    {
        try {
            $warehouse = $this->model->findOrFail($warehouseId);

            $area = $warehouse->areas()->create([
                'name' => $data['name'],
                'code' => $data['code'],
                'description' => $data['description'] ?? null,
                'is_active' => $data['is_active'] ?? true,
                'created_by' => auth()->id(),
            ]);

            return [
                'success' => true,
                'message' => 'Khu vực kho đã được tạo thành công',
                'data' => $area
            ];
        } catch (\Exception $e) {
            Log::error('Error creating warehouse area: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tạo khu vực kho: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Update a warehouse area
     *
     * @param int $warehouseId
     * @param int $areaId
     * @param array $data
     * @return bool
     */
    public function updateWarehouseArea($warehouseId, $areaId, array $data)
    {
        try {
            $warehouse = $this->model->findOrFail($warehouseId);
            $area = $warehouse->areas()->findOrFail($areaId);

            $updated = $area->update([
                'name' => $data['name'],
                'code' => $data['code'],
                'description' => $data['description'] ?? $area->description,
                'is_active' => $data['is_active'] ?? $area->is_active,
                'updated_by' => auth()->id(),
            ]);

            return [
                'success' => true,
                'message' => 'Khu vực kho đã được cập nhật thành công',
                'data' => $area
            ];
        } catch (\Exception $e) {
            Log::error('Error updating warehouse area: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật khu vực kho: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Delete a warehouse area
     *
     * @param int $warehouseId
     * @param int $areaId
     * @return bool
     */
    public function deleteWarehouseArea($warehouseId, $areaId)
    {
        try {
            $warehouse = $this->model->findOrFail($warehouseId);
            $area = $warehouse->areas()->findOrFail($areaId);

            // Check if area has inventory
            if ($area->inventoryItems()->count() > 0) {
                throw new \Exception('Không thể xóa khu vực đang có hàng tồn kho');
            }

            $deleted = $area->delete();

            return [
                'success' => true,
                'message' => 'Khu vực kho đã được xóa thành công'
            ];
        } catch (\Exception $e) {
            Log::error('Error deleting warehouse area: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get warehouse areas for datatable
     *
     * @param int $warehouseId
     * @param Request $request
     * @return array
     */
    public function getWarehouseAreasForDatatable($warehouseId, Request $request)
    {
        try {
            $warehouse = $this->model->findOrFail($warehouseId);
            $query = $warehouse->areas();

            // Search
            if ($request->has('search') && !empty($request->search['value'])) {
                $searchValue = $request->search['value'];
                $query->where(function ($q) use ($searchValue) {
                    $q->where('name', 'like', "%{$searchValue}%")
                        ->orWhere('code', 'like', "%{$searchValue}%")
                        ->orWhere('description', 'like', "%{$searchValue}%");
                });
            }

            // Filter by status
            if ($request->has('status') && $request->status !== 'all') {
                $query->where('is_active', $request->status === 'active');
            }

            $recordsTotal = $warehouse->areas()->count();
            $recordsFiltered = $query->count();

            // Order
            if ($request->has('order') && !empty($request->order)) {
                $order = $request->order[0];
                $columnIndex = $order['column'];
                $columnName = $request->columns[$columnIndex]['data'];
                $columnDirection = $order['dir'];

                if ($columnName !== 'actions') {
                    $query->orderBy($columnName, $columnDirection);
                }
            } else {
                $query->orderBy('name', 'asc');
            }

            // Pagination
            $start = $request->start ?? 0;
            $length = $request->length ?? 10;

            if ($length != -1) {
                $query->skip($start)->take($length);
            }

            $areas = $query->get();

            $data = [];
            foreach ($areas as $area) {
                $data[] = [
                    'id' => $area->id,
                    'name' => $area->name,
                    'code' => $area->code,
                    'description' => $area->description,
                    'is_active' => $area->is_active,
                    'created_at' => $area->created_at->format('d/m/Y H:i:s'),
                    'actions' => '' // Will be rendered on the client side
                ];
            }

            return [
                'draw' => intval($request->draw),
                'recordsTotal' => $recordsTotal,
                'recordsFiltered' => $recordsFiltered,
                'data' => $data
            ];
        } catch (\Exception $e) {
            Log::error('Error getting warehouse areas for datatable: ' . $e->getMessage());

            return [
                'draw' => intval($request->draw),
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'data' => []
            ];
        }
    }
}
