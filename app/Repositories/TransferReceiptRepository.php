<?php

namespace App\Repositories;

use App\Contracts\TransferReceiptInterface;
use App\Models\TransferReceipt;
use App\Models\TransferReceiptItem;
use App\Models\TransferOrder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TransferReceiptRepository extends BaseRepository implements TransferReceiptInterface
{
    /**
     * TransferReceiptRepository constructor.
     */
    public function __construct(TransferReceipt $model)
    {
        parent::__construct($model);
    }

    /**
     * Get all transfer receipts with pagination
     */
    public function getAllWithPagination($perPage = 15)
    {
        return $this->model->with(['transferOrder', 'fromWarehouse', 'toWarehouse', 'transitWarehouse', 'createdBy', 'approvedBy'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Get transfer receipt by ID
     */
    public function getById($id)
    {
        return $this->model->with(['transferOrder', 'fromWarehouse', 'toWarehouse', 'transitWarehouse', 'createdBy', 'approvedBy', 'items.product'])
            ->findOrFail($id);
    }

    /**
     * Create new transfer receipt
     */
    public function create(array $data)
    {
        try {
            DB::beginTransaction();

            // Generate code if not provided
            if (!isset($data['code'])) {
                $data['code'] = TransferReceipt::generateCode();
            }

            $transferReceipt = $this->model->create($data);

            // Create items if provided
            if (isset($data['items']) && is_array($data['items'])) {
                $this->createItems($transferReceipt->id, $data['items']);
            }

            DB::commit();
            return $transferReceipt;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating transfer receipt: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update transfer receipt
     */
    public function update($id, array $data)
    {
        try {
            DB::beginTransaction();

            $transferReceipt = $this->getById($id);
            $transferReceipt->update($data);

            // Update items if provided
            if (isset($data['items']) && is_array($data['items'])) {
                $this->updateItems($transferReceipt->id, $data['items']);
            }

            DB::commit();
            return $transferReceipt;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating transfer receipt: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete transfer receipt
     */
    public function delete($id)
    {
        try {
            DB::beginTransaction();

            $transferReceipt = $this->getById($id);

            // Delete items first
            $transferReceipt->items()->delete();

            // Delete transfer receipt
            $transferReceipt->delete();

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting transfer receipt: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get transfer receipts for datatable
     */
    public function getForDatatable(array $params)
    {
        try {
            $query = DB::table('transfer_receipts as trc')
                ->leftJoin('transfer_orders as to', 'trc.transfer_order_id', '=', 'to.id')
                ->leftJoin('warehouses as wf', 'trc.from_warehouse_id', '=', 'wf.id')
                ->leftJoin('warehouses as wt', 'trc.to_warehouse_id', '=', 'wt.id')
                ->leftJoin('warehouses as wtr', 'trc.transit_warehouse_id', '=', 'wtr.id')
                ->leftJoin('users as uc', 'trc.created_by', '=', 'uc.id')
                ->leftJoin('users as ua', 'trc.approved_by', '=', 'ua.id')
                ->select(
                    'trc.id',
                    'trc.code',
                    'trc.status',
                    'trc.notes',
                    'trc.created_at',
                    'trc.approved_at',
                    'to.code as transfer_order_code',
                    'wf.name as from_warehouse_name',
                    'wt.name as to_warehouse_name',
                    'wtr.name as transit_warehouse_name',
                    'uc.name as created_by_name',
                    'ua.name as approved_by_name'
                )
                ->whereNull('trc.deleted_at');

            // Apply filters
            if (isset($params['status']) && !empty($params['status'])) {
                $query->where('trc.status', $params['status']);
            }

            if (isset($params['from_warehouse_id']) && !empty($params['from_warehouse_id'])) {
                $query->where('trc.from_warehouse_id', $params['from_warehouse_id']);
            }

            if (isset($params['to_warehouse_id']) && !empty($params['to_warehouse_id'])) {
                $query->where('trc.to_warehouse_id', $params['to_warehouse_id']);
            }

            if (isset($params['search']['value']) && !empty($params['search']['value'])) {
                $searchValue = $params['search']['value'];
                $query->where(function ($q) use ($searchValue) {
                    $q->where('trc.code', 'like', "%{$searchValue}%")
                      ->orWhere('to.code', 'like', "%{$searchValue}%")
                      ->orWhere('wf.name', 'like', "%{$searchValue}%")
                      ->orWhere('wt.name', 'like', "%{$searchValue}%")
                      ->orWhere('uc.name', 'like', "%{$searchValue}%");
                });
            }

            // Get total count before pagination
            $totalRecords = $query->count();

            // Apply ordering
            if (isset($params['order'][0]['column']) && isset($params['columns'][$params['order'][0]['column']]['data'])) {
                $orderColumn = $params['columns'][$params['order'][0]['column']]['data'];
                $orderDirection = $params['order'][0]['dir'] ?? 'asc';

                $orderColumnMap = [
                    'code' => 'trc.code',
                    'transfer_order_code' => 'to.code',
                    'from_warehouse' => 'wf.name',
                    'to_warehouse' => 'wt.name',
                    'status' => 'trc.status',
                    'created_at' => 'trc.created_at',
                    'created_by' => 'uc.name'
                ];

                if (isset($orderColumnMap[$orderColumn])) {
                    $query->orderBy($orderColumnMap[$orderColumn], $orderDirection);
                }
            } else {
                $query->orderBy('trc.created_at', 'desc');
            }

            // Apply pagination
            if (isset($params['start']) && isset($params['length'])) {
                $query->offset($params['start'])->limit($params['length']);
            }

            $data = $query->get();

            return [
                'data' => $data,
                'recordsTotal' => $totalRecords,
                'recordsFiltered' => $totalRecords
            ];
        } catch (\Exception $e) {
            Log::error('Error getting transfer receipts for datatable: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Submit transfer receipt for approval
     */
    public function submitForApproval($id)
    {
        $transferReceipt = $this->getById($id);

        if (!$transferReceipt->canBeSubmitted()) {
            throw new \Exception('Phiếu nhận hàng không thể gửi duyệt.');
        }

        $transferReceipt->update(['status' => TransferReceipt::STATUS_PENDING]);
        return $transferReceipt;
    }

    /**
     * Approve transfer receipt
     */
    public function approve($id, $approvedBy)
    {
        try {
            DB::beginTransaction();

            $transferReceipt = $this->getById($id);

            if (!$transferReceipt->canBeApproved()) {
                throw new \Exception('Phiếu nhận hàng không thể duyệt.');
            }

            // Update transfer receipt status
            $transferReceipt->update([
                'status' => TransferReceipt::STATUS_APPROVED,
                'approved_by' => $approvedBy,
                'approved_at' => now()
            ]);

            // Process inventory transactions
            $this->processInventoryTransactions($transferReceipt);

            DB::commit();
            return $transferReceipt;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error approving transfer receipt: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Process inventory transactions when transfer receipt is approved
     */
    private function processInventoryTransactions($transferReceipt)
    {
        // This will be implemented to handle inventory movements
        // For now, we'll just log the action
        Log::info('Processing inventory transactions for transfer receipt: ' . $transferReceipt->code);

        // TODO: Implement inventory transaction logic
        // - Decrease inventory in transit warehouse
        // - Increase inventory in destination warehouse
    }

    /**
     * Reject transfer receipt
     */
    public function reject($id, $rejectedBy, $reason = null)
    {
        $transferReceipt = $this->getById($id);

        if (!$transferReceipt->canBeRejected()) {
            throw new \Exception('Phiếu nhận hàng không thể từ chối.');
        }

        $updateData = [
            'status' => TransferReceipt::STATUS_REJECTED,
            'approved_by' => $rejectedBy,
            'approved_at' => now()
        ];

        if ($reason) {
            $updateData['notes'] = ($transferReceipt->notes ? $transferReceipt->notes . "\n\n" : '') . "Lý do từ chối: " . $reason;
        }

        $transferReceipt->update($updateData);
        return $transferReceipt;
    }

    /**
     * Cancel transfer receipt
     */
    public function cancel($id)
    {
        $transferReceipt = $this->getById($id);

        if (!$transferReceipt->canBeCancelled()) {
            throw new \Exception('Phiếu nhận hàng không thể hủy.');
        }

        $transferReceipt->update(['status' => TransferReceipt::STATUS_CANCELLED]);
        return $transferReceipt;
    }

    /**
     * Create transfer receipt items
     */
    public function createItems($transferReceiptId, array $items)
    {
        try {
            foreach ($items as $item) {
                $item['transfer_receipt_id'] = $transferReceiptId;
                TransferReceiptItem::create($item);
            }
            return true;
        } catch (\Exception $e) {
            Log::error('Error creating transfer receipt items: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update transfer receipt items
     */
    public function updateItems($transferReceiptId, array $items)
    {
        try {
            // Delete existing items
            TransferReceiptItem::where('transfer_receipt_id', $transferReceiptId)->delete();

            // Create new items
            $this->createItems($transferReceiptId, $items);

            return true;
        } catch (\Exception $e) {
            Log::error('Error updating transfer receipt items: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete transfer receipt items
     */
    public function deleteItems($transferReceiptId, array $itemIds = [])
    {
        try {
            $query = TransferReceiptItem::where('transfer_receipt_id', $transferReceiptId);

            if (!empty($itemIds)) {
                $query->whereIn('id', $itemIds);
            }

            $query->delete();
            return true;
        } catch (\Exception $e) {
            Log::error('Error deleting transfer receipt items: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create transfer receipt from transfer order
     */
    public function createFromTransferOrder($transferOrderId, array $data)
    {
        try {
            DB::beginTransaction();

            $transferOrder = TransferOrder::with('items')->findOrFail($transferOrderId);

            // Prepare transfer receipt data
            $transferReceiptData = [
                'transfer_order_id' => $transferOrderId,
                'from_warehouse_id' => $transferOrder->from_warehouse_id,
                'to_warehouse_id' => $transferOrder->to_warehouse_id,
                'transit_warehouse_id' => $transferOrder->transit_warehouse_id,
                'notes' => $data['notes'] ?? $transferOrder->notes,
                'created_by' => $data['created_by'],
            ];

            $transferReceipt = $this->create($transferReceiptData);

            // Copy items from transfer order
            $items = [];
            foreach ($transferOrder->items as $orderItem) {
                $items[] = [
                    'product_id' => $orderItem->product_id,
                    'quantity' => $orderItem->quantity,
                    'notes' => $orderItem->notes,
                ];
            }

            if (!empty($items)) {
                $this->createItems($transferReceipt->id, $items);
            }

            DB::commit();
            return $transferReceipt;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating transfer receipt from transfer order: ' . $e->getMessage());
            throw $e;
        }
    }
}
