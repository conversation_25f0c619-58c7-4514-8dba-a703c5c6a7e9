<?php

namespace App\Repositories;

use App\Contracts\GoodReceiptInterface;
use App\Models\GoodReceipt;
use App\Models\GoodReceiptItem;
use App\Models\GoodReceiptAttachment;
use App\Models\InventoryTransaction;
use App\Models\InventoryItem;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderItem;
use App\Models\ProductSerial;
use App\Models\ProductBatch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class GoodReceiptRepository implements GoodReceiptInterface
{
    protected $model;

    public function __construct(GoodReceipt $model)
    {
        $this->model = $model;
    }

    /**
     * Get all good receipts
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAll()
    {
        return $this->model->all();
    }

    /**
     * Get good receipt by ID
     *
     * @param int $id
     * @return \App\Models\GoodReceipt
     */
    public function findById($id)
    {
        return $this->model->with([
            'items.product',
            'items.warehouseArea',
            'items.serials',
            'items.batches',
            'warehouse',
            'purchaseOrder',
            'createdBy'
        ])->findOrFail($id);
    }

    /**
     * Create a new good receipt
     *
     * @param array $data
     * @return \App\Models\GoodReceipt
     */
    public function create(array $data)
    {
        try {
            DB::beginTransaction();

            // Create good receipt
            $goodReceipt = $this->model->create([
                'code' => $data['code'],
                'purchase_order_id' => $data['purchase_order_id'] ?? null,
                'warehouse_id' => $data['warehouse_id'],
                'status' => $data['status'] ?? GoodReceipt::STATUS_DRAFT,
                'notes' => $data['notes'] ?? null,
                'created_by' => $data['created_by'],
                'has_documents' => $data['has_documents'] ?? false,
                'invoice_number' => $data['invoice_number'] ?? null,
                'invoice_series' => $data['invoice_series'] ?? null,
                'invoice_template' => $data['invoice_template'] ?? null,
                'invoice_date' => $data['invoice_date'] ?? null,
            ]);

            // Chuẩn bị dữ liệu cho bulk insert
            $goodReceiptItems = [];
            $inventoryTransactions = [];
            $serialsToUpdate = [];
            $serialsToCreate = [];
            $inventoryUpdates = [];
            $purchaseOrderItemUpdates = [];

            // Tạo các item cho phiếu nhập kho
            foreach ($data['items'] as $item) {
                // Tạo good receipt item
                $goodReceiptItem = new GoodReceiptItem([
                    'good_receipt_id' => $goodReceipt->id,
                    'purchase_order_item_id' => $item['purchase_order_item_id'] ?? null,
                    'product_id' => $item['product_id'],
                    'warehouse_area_id' => $item['warehouse_area_id'] ?? null,
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'] ?? 0,
                    'tax_rate' => $item['tax_rate'] ?? 0,
                    'unit_price_after_tax' => $item['unit_price_after_tax'] ?? 0,
                    'discount' => $item['discount'] ?? 0,
                    'batch_number' => $item['batch_number'] ?? null,
                    'expiry_date' => $item['expiry_date'] ?? null,
                    'notes' => $item['notes'] ?? null,
                ]);
                $goodReceiptItem->save();

                // Chỉ cập nhật tồn kho nếu trạng thái là COMPLETED
                if ($goodReceipt->status === GoodReceipt::STATUS_COMPLETED) {
                    // Cập nhật hoặc tạo mới inventory item
                    $inventoryItem = InventoryItem::firstOrNew([
                        'warehouse_id' => $data['warehouse_id'],
                        'product_id' => $item['product_id'],
                        'warehouse_area_id' => $item['warehouse_area_id'] ?? null,
                    ]);

                    if (!$inventoryItem->exists) {
                        $inventoryItem->quantity = $item['quantity'];
                        $inventoryItem->save();
                    } else {
                        $inventoryItem->quantity += $item['quantity'];
                        $inventoryItem->save();
                    }

                    // Tạo inventory transaction
                    $inventoryTransaction = new InventoryTransaction([
                        'warehouse_id' => $data['warehouse_id'],
                        'product_id' => $item['product_id'],
                        'warehouse_area_id' => $item['warehouse_area_id'] ?? null,
                        'quantity' => $item['quantity'],
                        'type' => 'in',
                        'batch_number' => $item['batch_number'] ?? null,
                        'expiry_date' => $item['expiry_date'] ?? null,
                        'reference_id' => $goodReceipt->id,
                        'reference_type' => 'good_receipt',
                        'notes' => 'Nhập kho từ phiếu nhập hàng',
                        'created_by' => $data['created_by'],
                    ]);
                    $inventoryTransaction->save();

                    // Xử lý serials nếu có
                    if (isset($item['serials']) && is_array($item['serials']) && count($item['serials']) > 0) {
                        foreach ($item['serials'] as $serial) {
                            $productSerial = new ProductSerial([
                                'product_id' => $item['product_id'],
                                'serial_number' => $serial,
                                'warehouse_id' => $data['warehouse_id'],
                                'warehouse_area_id' => $item['warehouse_area_id'] ?? null,
                                'purchase_order_item_id' => $item['purchase_order_item_id'] ?? null,
                                'good_receipt_item_id' => $goodReceiptItem->id,
                                'status' => 'in_stock',
                                'notes' => 'Nhập kho từ phiếu nhập hàng',
                            ]);
                            $productSerial->save();
                        }
                    }
                }
            }

            DB::commit();

            return $goodReceipt;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating good receipt: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update good receipt
     *
     * @param int $id
     * @param array $data
     * @return \App\Models\GoodReceipt
     */
    public function update($id, array $data)
    {
        try {
            $goodReceipt = $this->findById($id);

            // Không cho phép cập nhật phiếu nhập kho đã hoàn thành hoặc đã hủy
            if (in_array($goodReceipt->status, [GoodReceipt::STATUS_COMPLETED, GoodReceipt::STATUS_CANCELLED])) {
                throw new \Exception('Không thể cập nhật phiếu nhập kho đã hoàn thành hoặc đã hủy.');
            }

            $updateData = [
                'notes' => $data['notes'] ?? $goodReceipt->notes,
            ];

            // Cập nhật thông tin hóa đơn nếu có
            if (isset($data['invoice_number'])) {
                $updateData['invoice_number'] = $data['invoice_number'];
            }

            if (isset($data['invoice_series'])) {
                $updateData['invoice_series'] = $data['invoice_series'];
            }

            if (isset($data['invoice_template'])) {
                $updateData['invoice_template'] = $data['invoice_template'];
            }

            if (isset($data['invoice_date'])) {
                $updateData['invoice_date'] = $data['invoice_date'];
            }

            if (isset($data['has_documents'])) {
                $updateData['has_documents'] = $data['has_documents'];
            }

            $goodReceipt->update($updateData);

            return $goodReceipt;
        } catch (\Exception $e) {
            Log::error('Error updating good receipt: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Submit good receipt for approval
     *
     * @param int $id
     * @return \App\Models\GoodReceipt
     */
    public function submit($id)
    {
        try {
            DB::beginTransaction();

            $goodReceipt = $this->findById($id);

            if (!$goodReceipt->canSubmit()) {
                throw new \Exception('Không thể gửi yêu cầu duyệt phiếu nhập kho này.');
            }

            // Kiểm tra xem đã có hóa đơn chứng từ chưa
            if (!$goodReceipt->hasRequiredDocuments()) {
                throw new \Exception('Vui lòng nhập thông tin hóa đơn chứng từ trước khi gửi yêu cầu duyệt.');
            }

            $goodReceipt->submit();

            DB::commit();

            return $goodReceipt;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error submitting good receipt: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Approve good receipt
     *
     * @param int $id
     * @param int $userId
     * @return \App\Models\GoodReceipt
     */
    public function approve($id, $userId)
    {
        try {
            DB::beginTransaction();

            $goodReceipt = $this->findById($id);

            if (!$goodReceipt->canApprove()) {
                throw new \Exception('Không thể duyệt phiếu nhập kho này.');
            }

            // Duyệt phiếu nhập kho
            $goodReceipt->approve($userId);

            // Cập nhật tồn kho
            foreach ($goodReceipt->items as $item) {
                // Cập nhật inventory item
                $inventoryItem = InventoryItem::firstOrNew([
                    'warehouse_id' => $goodReceipt->warehouse_id,
                    'product_id' => $item->product_id,
                    'warehouse_area_id' => $item->warehouse_area_id,
                ]);

                if (!$inventoryItem->exists) {
                    $inventoryItem->quantity = $item->quantity;
                    $inventoryItem->save();
                } else {
                    $inventoryItem->quantity += $item->quantity;
                    $inventoryItem->save();
                }

                // Kiểm tra xem sản phẩm có được quản lý theo batch không
                $product = $item->product;
                if ($product && $product->isTrackedByBatch()) {
                    // Lấy danh sách batch của item
                    $batches = ProductBatch::where('good_receipt_item_id', $item->id)->get();

                    if ($batches->count() > 0) {
                        // Tạo inventory transaction cho từng batch
                        foreach ($batches as $batch) {
                            InventoryTransaction::create([
                                'transaction_type' => 'in',
                                'product_id' => $item->product_id,
                                'warehouse_id' => $goodReceipt->warehouse_id,
                                'warehouse_area_id' => $item->warehouse_area_id,
                                'quantity' => $batch->quantity,
                                'batch_number' => $batch->batch_number,
                                'product_batch_id' => $batch->id,
                                'expiry_date' => $batch->expiry_date,
                                'reference_type' => 'good_receipt',
                                'reference_id' => $goodReceipt->id,
                                'purchase_order_id' => $goodReceipt->purchase_order_id,
                                'notes' => 'Duyệt phiếu nhập kho - Batch: ' . $batch->batch_number,
                                'created_by' => $userId,
                            ]);
                        }
                    } else {
                        // Nếu không có batch nào, tạo inventory transaction cho cả item
                        InventoryTransaction::create([
                            'transaction_type' => 'in',
                            'product_id' => $item->product_id,
                            'warehouse_id' => $goodReceipt->warehouse_id,
                            'warehouse_area_id' => $item->warehouse_area_id,
                            'quantity' => $item->quantity,
                            'batch_number' => $item->batch_number,
                            'expiry_date' => $item->expiry_date,
                            'reference_type' => 'good_receipt',
                            'reference_id' => $goodReceipt->id,
                            'purchase_order_id' => $goodReceipt->purchase_order_id,
                            'notes' => 'Duyệt phiếu nhập kho',
                            'created_by' => $userId,
                        ]);
                    }
                } else {
                    // Đối với sản phẩm không quản lý theo batch, tạo inventory transaction bình thường
                    InventoryTransaction::create([
                        'transaction_type' => 'in',
                        'product_id' => $item->product_id,
                        'warehouse_id' => $goodReceipt->warehouse_id,
                        'warehouse_area_id' => $item->warehouse_area_id,
                        'quantity' => $item->quantity,
                        'batch_number' => $item->batch_number,
                        'expiry_date' => $item->expiry_date,
                        'reference_type' => 'good_receipt',
                        'reference_id' => $goodReceipt->id,
                        'purchase_order_id' => $goodReceipt->purchase_order_id,
                        'notes' => 'Duyệt phiếu nhập kho',
                        'created_by' => $userId,
                    ]);
                }

                // Cập nhật số lượng đã nhập cho purchase order item nếu có
                if ($item->purchase_order_item_id) {
                    $purchaseOrderItem = PurchaseOrderItem::find($item->purchase_order_item_id);
                    if ($purchaseOrderItem) {
                        $purchaseOrderItem->received_quantity += $item->quantity;
                        $purchaseOrderItem->save();
                    }
                }

                // Cập nhật trạng thái serials
                $serials = ProductSerial::where('good_receipt_item_id', $item->id)->get();
                foreach ($serials as $serial) {
                    $serial->status = 'in_stock';
                    $serial->save();
                }
            }

            // Cập nhật trạng thái purchase order nếu có
            if ($goodReceipt->purchase_order_id) {
                $purchaseOrder = PurchaseOrder::find($goodReceipt->purchase_order_id);
                if ($purchaseOrder) {
                    $purchaseOrder->updateStatusBasedOnReceivedItems();
                }
            }

            DB::commit();

            return $goodReceipt;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error approving good receipt: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete good receipt
     *
     * @param int $id
     * @return bool
     */
    public function delete($id)
    {
        try {
            $goodReceipt = $this->findById($id);

            // Không cho phép xóa phiếu nhập kho đã hoàn thành
            if ($goodReceipt->status === GoodReceipt::STATUS_COMPLETED) {
                throw new \Exception('Không thể xóa phiếu nhập kho đã hoàn thành.');
            }

            $goodReceipt->delete();

            return true;
        } catch (\Exception $e) {
            Log::error('Error deleting good receipt: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get good receipts for datatable
     *
     * @param Request $request
     * @return array
     */
    public function getGoodReceiptsForDatatable(Request $request)
    {
        try {
            $draw = $request->get('draw');
            $start = $request->get('start') ?? 0;
            $length = $request->get('length') ?? 10;
            $order = $request->get('order');
            $columns = $request->get('columns');
            $search = $request->get('search');

            $columnIndex = $order[0]['column'] ?? 0;
            $columnName = $columns[$columnIndex]['data'] ?? 'created_at';
            $columnSortOrder = $order[0]['dir'] ?? 'desc';
            $searchValue = $search['value'] ?? '';

            // Sử dụng query builder trực tiếp để tối ưu hiệu suất
            $query = DB::table('good_receipts')
                ->select(
                    'good_receipts.id',
                    'good_receipts.code',
                    'good_receipts.warehouse_id',
                    'good_receipts.purchase_order_id',
                    'good_receipts.status',
                    'good_receipts.created_by',
                    'good_receipts.created_at',
                    'warehouses.name as warehouse_name',
                    'purchase_orders.code as purchase_order_code',
                    'users.name as created_by_name'
                )
                ->leftJoin('warehouses', 'good_receipts.warehouse_id', '=', 'warehouses.id')
                ->leftJoin('purchase_orders', 'good_receipts.purchase_order_id', '=', 'purchase_orders.id')
                ->leftJoin('users', 'good_receipts.created_by', '=', 'users.id')
                ->whereNull('good_receipts.deleted_at');

            // Apply filters
            if ($request->has('warehouse_id') && $request->warehouse_id) {
                $query->where('good_receipts.warehouse_id', $request->warehouse_id);
            }

            if ($request->has('purchase_order_id') && $request->purchase_order_id) {
                $query->where('good_receipts.purchase_order_id', $request->purchase_order_id);
            }

            if ($request->has('status') && $request->status) {
                $query->where('good_receipts.status', $request->status);
            }

            if ($request->has('date_from') && $request->date_from) {
                $query->whereDate('good_receipts.created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to') && $request->date_to) {
                $query->whereDate('good_receipts.created_at', '<=', $request->date_to);
            }

            // Apply search
            if ($searchValue) {
                $query->where(function ($q) use ($searchValue) {
                    $q->where('good_receipts.code', 'like', '%' . $searchValue . '%')
                        ->orWhere('warehouses.name', 'like', '%' . $searchValue . '%')
                        ->orWhere('purchase_orders.code', 'like', '%' . $searchValue . '%')
                        ->orWhere('users.name', 'like', '%' . $searchValue . '%');
                });
            }

            // Count total records
            $totalRecords = $query->count();
            $totalRecordsWithFilter = $totalRecords;

            // Apply pagination and ordering
            $records = $query->orderBy($columnName, $columnSortOrder)
                ->offset($start)
                ->limit($length)
                ->get();

            $data = [];
            foreach ($records as $record) {
                $data[] = [
                    'id' => $record->id,
                    'code' => $record->code,
                    'created_at' => date('d/m/Y', strtotime($record->created_at)),
                    'warehouse' => [
                        'id' => $record->warehouse_id,
                        'name' => $record->warehouse_name
                    ],
                    'purchase_order' => $record->purchase_order_id ? [
                        'id' => $record->purchase_order_id,
                        'code' => $record->purchase_order_code
                    ] : null,
                    'status' => $record->status,
                    'created_by' => [
                        'id' => $record->created_by,
                        'name' => $record->created_by_name
                    ]
                ];
            }

            return [
                'draw' => intval($draw),
                'recordsTotal' => $totalRecords,
                'recordsFiltered' => $totalRecordsWithFilter,
                'data' => $data
            ];
        } catch (\Exception $e) {
            Log::error('Error getting good receipts for datatable: ' . $e->getMessage());
            return [
                'draw' => intval($request->draw),
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'data' => []
            ];
        }
    }

    /**
     * Get good receipt items
     *
     * @param int $goodReceiptId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getGoodReceiptItems($goodReceiptId)
    {
        return GoodReceiptItem::with(['product', 'warehouseArea', 'purchaseOrderItem'])
            ->where('good_receipt_id', $goodReceiptId)
            ->get();
    }

    /**
     * Cancel good receipt
     *
     * @param int $id
     * @param string $reason
     * @return \App\Models\GoodReceipt
     */
    public function cancel($id, $reason)
    {
        try {
            DB::beginTransaction();

            $goodReceipt = $this->findById($id);

            // Kiểm tra xem phiếu nhập kho có thể hủy không
            if (!$goodReceipt->canBeCancelled()) {
                throw new \Exception('Không thể hủy phiếu nhập kho này.');
            }

            // Cập nhật trạng thái phiếu nhập kho
            $goodReceipt->cancel($reason);

            // Cập nhật lại số lượng tồn kho
            foreach ($goodReceipt->items as $item) {
                // Cập nhật inventory item
                $inventoryItem = InventoryItem::where('warehouse_id', $goodReceipt->warehouse_id)
                    ->where('product_id', $item->product_id)
                    ->where('warehouse_area_id', $item->warehouse_area_id)
                    ->first();

                if ($inventoryItem) {
                    $inventoryItem->quantity -= $item->quantity;
                    $inventoryItem->save();
                }

                // Tạo inventory transaction
                $inventoryTransaction = new InventoryTransaction([
                    'warehouse_id' => $goodReceipt->warehouse_id,
                    'product_id' => $item->product_id,
                    'warehouse_area_id' => $item->warehouse_area_id,
                    'quantity' => -$item->quantity,
                    'type' => 'out',
                    'reference_id' => $goodReceipt->id,
                    'reference_type' => 'good_receipt_cancel',
                    'notes' => 'Hủy phiếu nhập kho: ' . $reason,
                    'created_by' => auth()->id(),
                ]);
                $inventoryTransaction->save();

                // Cập nhật lại số lượng đã nhận trong purchase order item
                if ($item->purchase_order_item_id) {
                    $purchaseOrderItem = PurchaseOrderItem::find($item->purchase_order_item_id);
                    if ($purchaseOrderItem) {
                        $purchaseOrderItem->received_quantity -= $item->quantity;
                        $purchaseOrderItem->save();
                    }
                }

                // Cập nhật trạng thái serials
                $serials = ProductSerial::where('good_receipt_item_id', $item->id)->get();
                foreach ($serials as $serial) {
                    $serial->status = 'cancelled';
                    $serial->notes = 'Hủy phiếu nhập kho: ' . $reason;
                    $serial->save();
                }
            }

            // Cập nhật lại trạng thái purchase order nếu phiếu nhập kho đã được duyệt
            if ($goodReceipt->purchase_order_id && $goodReceipt->status === GoodReceipt::STATUS_COMPLETED) {
                $purchaseOrder = PurchaseOrder::find($goodReceipt->purchase_order_id);
                if ($purchaseOrder) {
                    $purchaseOrder->updateStatusBasedOnReceivedItems();
                }
            }

            DB::commit();

            return $goodReceipt;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error cancelling good receipt: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Save import data for good receipt
     *
     * @param int $id
     * @param array $importData
     * @return array
     */
    public function saveImportData($id, array $importData)
    {
        try {
            DB::beginTransaction();

            $goodReceipt = $this->findById($id);

            // Kiểm tra trạng thái phiếu nhập kho
            if ($goodReceipt->status !== GoodReceipt::STATUS_DRAFT) {
                throw new \Exception('Chỉ có thể lưu dữ liệu cho phiếu nhập kho ở trạng thái nháp.');
            }

            // Xóa các serial cũ của phiếu nhập kho
            // Lấy danh sách các item_id của phiếu nhập kho
            $goodReceiptItemIds = $goodReceipt->items->pluck('id')->toArray();

            // Xóa các serial có good_receipt_item_id nằm trong danh sách
            if (!empty($goodReceiptItemIds)) {
                ProductSerial::whereIn('good_receipt_item_id', $goodReceiptItemIds)->delete();
            }

            $processedItems = [];

            // Xử lý dữ liệu nhập kho
            foreach ($importData as $itemId => $data) {
                $item = GoodReceiptItem::find($itemId);

                if (!$item || $item->good_receipt_id != $goodReceipt->id) {
                    continue;
                }

                // Cập nhật warehouse_area_id cho item
                if (isset($data['warehouseAreaId'])) {
                    $item->warehouse_area_id = $data['warehouseAreaId'];
                    $item->save();
                }

                // Xử lý theo loại dữ liệu
                switch ($data['type']) {
                    case 'quantity':
                        // Đã cập nhật warehouse_area_id ở trên
                        $processedItems[$itemId] = [
                            'type' => 'quantity',
                            'quantity' => $data['quantity'],
                            'warehouse_area_id' => $data['warehouseAreaId']
                        ];
                        break;

                    case 'batch':
                        // Xử lý dữ liệu batch
                        if (isset($data['batches']) && is_array($data['batches']) && count($data['batches']) > 0) {
                            // Lưu thông tin batch vào bảng product_batches
                            $batches = [];
                            $totalQuantity = 0;

                            foreach ($data['batches'] as $batchData) {
                                if (!isset($batchData['batchNumber']) || empty($batchData['batchNumber'])) {
                                    continue; // Bỏ qua batch không có số lô
                                }

                                $batchQuantity = isset($batchData['quantity']) ? (float)$batchData['quantity'] : 0;
                                $totalQuantity += $batchQuantity;

                                // Tạo hoặc cập nhật batch
                                $batch = ProductBatch::create([
                                    'batch_number' => $batchData['batchNumber'],
                                    'product_id' => $item->product_id,
                                    'warehouse_id' => $goodReceipt->warehouse_id,
                                    'warehouse_area_id' => $data['warehouseAreaId'],
                                    'quantity' => $batchQuantity,
                                    'initial_quantity' => $batchQuantity,
                                    'expiry_date' => isset($batchData['expiryDate']) ? $batchData['expiryDate'] : null,
                                    'manufacturing_date' => isset($batchData['manufacturingDate']) ? $batchData['manufacturingDate'] : null,
                                    'cost_price' => $item->unit_price,
                                    'good_receipt_item_id' => $item->id,
                                    'status' => 'active',
                                    'notes' => 'Nhập kho từ phiếu nhập ' . $goodReceipt->reference_number
                                ]);

                                $batches[] = $batch;
                            }

                            // Lưu batch đầu tiên vào item để tương thích ngược
                            if (count($batches) > 0) {
                                $firstBatch = $batches[0];
                                $item->batch_number = $firstBatch->batch_number;
                                $item->expiry_date = $firstBatch->expiry_date;
                            }

                            // Đảm bảo tổng số lượng batch bằng số lượng item
                            if ($totalQuantity != $data['quantity']) {
                                // Cập nhật lại số lượng của batch đầu tiên để đảm bảo tổng số lượng đúng
                                if (count($batches) > 0) {
                                    $diff = $data['quantity'] - $totalQuantity;
                                    $firstBatch->quantity += $diff;
                                    $firstBatch->initial_quantity += $diff;
                                    $firstBatch->save();
                                }
                            }
                        } else {
                            // Trường hợp cũ (tương thích ngược)
                            if (isset($data['batchNumber']) && !empty($data['batchNumber'])) {
                                $item->batch_number = $data['batchNumber'];

                                // Tạo batch trong bảng product_batches
                                $batch = ProductBatch::create([
                                    'batch_number' => $data['batchNumber'],
                                    'product_id' => $item->product_id,
                                    'warehouse_id' => $goodReceipt->warehouse_id,
                                    'warehouse_area_id' => $data['warehouseAreaId'],
                                    'quantity' => $data['quantity'],
                                    'initial_quantity' => $data['quantity'],
                                    'expiry_date' => isset($data['expiryDate']) ? $data['expiryDate'] : null,
                                    'cost_price' => $item->unit_price,
                                    'good_receipt_item_id' => $item->id,
                                    'status' => 'active',
                                    'notes' => 'Nhập kho từ phiếu nhập ' . $goodReceipt->reference_number
                                ]);
                            }

                            if (isset($data['expiryDate'])) {
                                $item->expiry_date = $data['expiryDate'];
                            }
                        }
                        $item->save();

                        $processedItems[$itemId] = [
                            'type' => 'batch',
                            'batches' => $data['batches'] ?? [],
                            'batch_number' => $item->batch_number,
                            'expiry_date' => $item->expiry_date,
                            'quantity' => $data['quantity'],
                            'warehouse_area_id' => $data['warehouseAreaId']
                        ];
                        break;

                    case 'serial':
                        // Tạo các serial mới
                        $serials = [];

                        // Chuẩn bị dữ liệu cho bulk insert
                        $serialsData = [];
                        $now = now();

                        foreach ($data['serials'] as $serialNumber) {
                            $serialsData[] = [
                                'product_id' => $item->product_id,
                                'serial_number' => $serialNumber,
                                'warehouse_id' => $goodReceipt->warehouse_id,
                                'warehouse_area_id' => $data['warehouseAreaId'],
                                'purchase_order_item_id' => $item->purchase_order_item_id,
                                'good_receipt_item_id' => $item->id,
                                'status' => 'pending', // Chỉ chuyển thành in_stock sau khi duyệt
                                'notes' => 'Nhập kho từ phiếu nhập hàng',
                                'created_at' => $now,
                                'updated_at' => $now,
                            ];
                            $serials[] = $serialNumber;
                        }

                        // Thực hiện bulk insert
                        if (!empty($serialsData)) {
                            try {
                                ProductSerial::insert($serialsData);
                            } catch (\Exception $e) {
                                // Xử lý trường hợp có serial trùng lặp
                                if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                                    // Thử insert từng serial một để bỏ qua các serial trùng lặp
                                    foreach ($serialsData as $serialData) {
                                        try {
                                            ProductSerial::create($serialData);
                                        } catch (\Exception $innerEx) {
                                            // Bỏ qua các serial trùng lặp
                                            Log::warning('Duplicate serial: ' . $serialData['serial_number']);
                                        }
                                    }
                                } else {
                                    // Nếu là lỗi khác, ném lại exception
                                    throw $e;
                                }
                            }
                        }

                        $processedItems[$itemId] = [
                            'type' => 'serial',
                            'serials' => $serials,
                            'warehouse_area_id' => $data['warehouseAreaId']
                        ];
                        break;
                }
            }

            DB::commit();

            return [
                'good_receipt' => $goodReceipt,
                'processed_items' => $processedItems
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error saving import data: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get good receipt attachments
     *
     * @param int $goodReceiptId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getGoodReceiptAttachments($goodReceiptId)
    {
        return GoodReceiptAttachment::where('good_receipt_id', $goodReceiptId)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Upload attachment for good receipt
     *
     * @param int $goodReceiptId
     * @param array $data
     * @return mixed
     */
    public function uploadAttachment($goodReceiptId, array $data)
    {
        try {
            DB::beginTransaction();

            $goodReceipt = $this->findById($goodReceiptId);

            // Kiểm tra trạng thái phiếu nhập kho
            if (!in_array($goodReceipt->status, [GoodReceipt::STATUS_DRAFT, GoodReceipt::STATUS_PENDING])) {
                return [
                    'success' => false,
                    'message' => 'Không thể tải lên tài liệu cho phiếu nhập kho này.'
                ];
            }

            $file = $data['file'];
            $fileName = $file->getClientOriginalName();
            $fileType = $file->getMimeType();
            $fileSize = $file->getSize();
            $filePath = $file->store('good-receipts/' . $goodReceiptId, 'public');

            // Create attachment
            $attachment = GoodReceiptAttachment::create([
                'good_receipt_id' => $goodReceiptId,
                'file_name' => $fileName,
                'file_path' => $filePath,
                'file_type' => $fileType,
                'file_size' => $fileSize,
                'description' => $data['description'] ?? null,
                'uploaded_by' => $data['uploaded_by'],
            ]);

            // Update has_documents flag
            $goodReceipt->has_documents = true;
            $goodReceipt->save();

            DB::commit();

            return [
                'success' => true,
                'message' => 'Tài liệu đã được tải lên thành công',
                'data' => $attachment
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error uploading attachment: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tải lên tài liệu: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Delete good receipt attachment
     *
     * @param int $goodReceiptId
     * @param int $attachmentId
     * @return bool
     */
    public function deleteAttachment($goodReceiptId, $attachmentId)
    {
        try {
            DB::beginTransaction();

            $goodReceipt = $this->findById($goodReceiptId);

            // Kiểm tra trạng thái phiếu nhập kho
            if (!in_array($goodReceipt->status, [GoodReceipt::STATUS_DRAFT, GoodReceipt::STATUS_PENDING])) {
                return false;
            }

            // Delete attachment
            $attachment = GoodReceiptAttachment::where('id', $attachmentId)
                ->where('good_receipt_id', $goodReceiptId)
                ->first();

            if (!$attachment) {
                return false;
            }

            // Delete file from storage
            if (Storage::disk('public')->exists($attachment->file_path)) {
                Storage::disk('public')->delete($attachment->file_path);
            }

            $attachment->delete();

            // Check if there are any attachments left
            $remainingAttachments = GoodReceiptAttachment::where('good_receipt_id', $goodReceiptId)->count();
            if ($remainingAttachments === 0) {
                $goodReceipt->has_documents = false;
                $goodReceipt->save();
            }

            DB::commit();

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting attachment: ' . $e->getMessage());
            return false;
        }
    }
}
