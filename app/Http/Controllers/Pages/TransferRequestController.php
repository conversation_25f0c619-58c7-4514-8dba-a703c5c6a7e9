<?php

namespace App\Http\Controllers\Pages;

use App\Contracts\TransferRequestInterface;
use App\Http\Controllers\Controller;
use App\Models\Warehouse;
use App\Models\Product;
use App\Models\InventoryItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TransferRequestController extends Controller
{
    protected $transferRequestRepository;

    public function __construct(TransferRequestInterface $transferRequestRepository)
    {
        $this->transferRequestRepository = $transferRequestRepository;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $pageConfigs = ['myLayout' => 'vertical'];

        $warehouses = Warehouse::active()->get();

        return view('content.pages.transfers.transfer-requests.index', compact('pageConfigs', 'warehouses'));
    }

    /**
     * Get data for DataTable
     */
    public function datatable(Request $request)
    {
        try {
            $params = $request->all();
            $result = $this->transferRequestRepository->getForDatatable($params);

            $data = [];
            foreach ($result['data'] as $item) {
                $statusBadge = match($item->status) {
                    'draft' => '<span class="badge bg-label-secondary">Nháp</span>',
                    'pending' => '<span class="badge bg-label-warning">Chờ duyệt</span>',
                    'approved' => '<span class="badge bg-label-success">Đã duyệt</span>',
                    'rejected' => '<span class="badge bg-label-danger">Từ chối</span>',
                    'cancelled' => '<span class="badge bg-label-dark">Đã hủy</span>',
                    default => '<span class="badge bg-label-secondary">Không xác định</span>',
                };

                $actions = '<div class="dropdown">
                    <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                        <i class="ri-more-2-line"></i>
                    </button>
                    <div class="dropdown-menu">
                        <a class="dropdown-item" href="' . route('transfer-requests.show', $item->id) . '">
                            <i class="ri-eye-line me-1"></i> Xem chi tiết
                        </a>';

                if (in_array($item->status, ['draft', 'pending'])) {
                    $actions .= '<a class="dropdown-item" href="' . route('transfer-requests.edit', $item->id) . '">
                        <i class="ri-pencil-line me-1"></i> Chỉnh sửa
                    </a>';
                }

                if ($item->status === 'draft') {
                    $actions .= '<a class="dropdown-item submit-approval-btn" href="javascript:void(0);" data-id="' . $item->id . '">
                        <i class="ri-send-plane-line me-1"></i> Gửi duyệt
                    </a>';
                }

                if ($item->status === 'pending') {
                    $actions .= '<a class="dropdown-item approve-btn" href="javascript:void(0);" data-id="' . $item->id . '">
                        <i class="ri-check-line me-1"></i> Duyệt
                    </a>
                    <a class="dropdown-item reject-btn" href="javascript:void(0);" data-id="' . $item->id . '">
                        <i class="ri-close-line me-1"></i> Từ chối
                    </a>';
                }

                if (in_array($item->status, ['draft', 'pending'])) {
                    $actions .= '<div class="dropdown-divider"></div>
                    <a class="dropdown-item text-danger cancel-btn" href="javascript:void(0);" data-id="' . $item->id . '">
                        <i class="ri-delete-bin-7-line me-1"></i> Hủy
                    </a>';
                }

                $actions .= '</div></div>';

                $data[] = [
                    'id' => $item->id,
                    'code' => $item->code,
                    'from_warehouse' => $item->from_warehouse_name,
                    'to_warehouse' => $item->to_warehouse_name,
                    'status' => $statusBadge,
                    'created_by' => $item->created_by_name,
                    'created_at' => date('d/m/Y H:i', strtotime($item->created_at)),
                    'actions' => $actions
                ];
            }

            return response()->json([
                'draw' => intval($request->draw),
                'recordsTotal' => $result['recordsTotal'],
                'recordsFiltered' => $result['recordsFiltered'],
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('Error in transfer request datatable: ' . $e->getMessage());
            return response()->json(['error' => 'Có lỗi xảy ra khi tải dữ liệu'], 500);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $pageConfigs = ['myLayout' => 'vertical'];

        $warehouses = Warehouse::active()->get();
        $products = Product::where('is_active', true)->get();

        return view('content.pages.transfers.transfer-requests.create', compact('pageConfigs', 'warehouses', 'products'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'from_warehouse_id' => 'required|exists:warehouses,id',
            'to_warehouse_id' => 'required|exists:warehouses,id|different:from_warehouse_id',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.notes' => 'nullable|string',
        ]);

        try {
            // Validate inventory availability
            foreach ($request->items as $item) {
                $inventoryItem = InventoryItem::where('warehouse_id', $request->from_warehouse_id)
                    ->where('product_id', $item['product_id'])
                    ->first();

                if (!$inventoryItem || $inventoryItem->available_quantity < $item['quantity']) {
                    $product = Product::find($item['product_id']);
                    return response()->json([
                        'success' => false,
                        'message' => "Sản phẩm {$product->name} không đủ số lượng tồn kho. Số lượng có sẵn: " . ($inventoryItem->available_quantity ?? 0)
                    ], 422);
                }
            }

            $data = $request->only(['from_warehouse_id', 'to_warehouse_id', 'notes']);
            $data['created_by'] = Auth::id();
            $data['items'] = $request->items;

            $transferRequest = $this->transferRequestRepository->create($data);

            return response()->json([
                'success' => true,
                'message' => 'Yêu cầu chuyển kho đã được tạo thành công.',
                'redirect' => route('transfer-requests.show', $transferRequest->id)
            ]);
        } catch (\Exception $e) {
            Log::error('Error creating transfer request: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tạo yêu cầu chuyển kho.'
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        try {
            $pageConfigs = ['myLayout' => 'vertical'];

            $transferRequest = $this->transferRequestRepository->getById($id);

            return view('content.pages.transfers.transfer-requests.show', compact('pageConfigs', 'transferRequest'));
        } catch (\Exception $e) {
            Log::error('Error showing transfer request: ' . $e->getMessage());
            return redirect()->route('transfer-requests.index')->with('error', 'Không tìm thấy yêu cầu chuyển kho.');
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        try {
            $pageConfigs = ['myLayout' => 'vertical'];

            $transferRequest = $this->transferRequestRepository->getById($id);

            if (!$transferRequest->canBeEdited()) {
                return redirect()->route('transfer-requests.show', $id)->with('error', 'Yêu cầu chuyển kho không thể chỉnh sửa.');
            }

            $warehouses = Warehouse::active()->get();
            $products = Product::where('is_active', true)->get();

            return view('content.pages.transfers.transfer-requests.edit', compact('pageConfigs', 'transferRequest', 'warehouses', 'products'));
        } catch (\Exception $e) {
            Log::error('Error editing transfer request: ' . $e->getMessage());
            return redirect()->route('transfer-requests.index')->with('error', 'Không tìm thấy yêu cầu chuyển kho.');
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'from_warehouse_id' => 'required|exists:warehouses,id',
            'to_warehouse_id' => 'required|exists:warehouses,id|different:from_warehouse_id',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.notes' => 'nullable|string',
        ]);

        try {
            $transferRequest = $this->transferRequestRepository->getById($id);

            if (!$transferRequest->canBeEdited()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Yêu cầu chuyển kho không thể chỉnh sửa.'
                ], 422);
            }

            // Validate inventory availability
            foreach ($request->items as $item) {
                $inventoryItem = InventoryItem::where('warehouse_id', $request->from_warehouse_id)
                    ->where('product_id', $item['product_id'])
                    ->first();

                if (!$inventoryItem || $inventoryItem->available_quantity < $item['quantity']) {
                    $product = Product::find($item['product_id']);
                    return response()->json([
                        'success' => false,
                        'message' => "Sản phẩm {$product->name} không đủ số lượng tồn kho. Số lượng có sẵn: " . ($inventoryItem->available_quantity ?? 0)
                    ], 422);
                }
            }

            $data = $request->only(['from_warehouse_id', 'to_warehouse_id', 'notes']);
            $data['items'] = $request->items;

            $this->transferRequestRepository->update($id, $data);

            return response()->json([
                'success' => true,
                'message' => 'Yêu cầu chuyển kho đã được cập nhật thành công.',
                'redirect' => route('transfer-requests.show', $id)
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating transfer request: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật yêu cầu chuyển kho.'
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            $transferRequest = $this->transferRequestRepository->getById($id);

            if (!$transferRequest->canBeEdited()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Yêu cầu chuyển kho không thể xóa.'
                ], 422);
            }

            $this->transferRequestRepository->delete($id);

            return response()->json([
                'success' => true,
                'message' => 'Yêu cầu chuyển kho đã được xóa thành công.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error deleting transfer request: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xóa yêu cầu chuyển kho.'
            ], 500);
        }
    }

    /**
     * Submit transfer request for approval
     */
    public function submitForApproval(Request $request, $id)
    {
        try {
            $this->transferRequestRepository->submitForApproval($id);

            return response()->json([
                'success' => true,
                'message' => 'Yêu cầu chuyển kho đã được gửi duyệt thành công.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error submitting transfer request for approval: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Approve transfer request
     */
    public function approve(Request $request, $id)
    {
        try {
            $this->transferRequestRepository->approve($id, Auth::id());

            return response()->json([
                'success' => true,
                'message' => 'Yêu cầu chuyển kho đã được duyệt thành công.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error approving transfer request: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reject transfer request
     */
    public function reject(Request $request, $id)
    {
        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        try {
            $this->transferRequestRepository->reject($id, Auth::id(), $request->reason);

            return response()->json([
                'success' => true,
                'message' => 'Yêu cầu chuyển kho đã được từ chối.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error rejecting transfer request: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel transfer request
     */
    public function cancel(Request $request, $id)
    {
        try {
            $this->transferRequestRepository->cancel($id);

            return response()->json([
                'success' => true,
                'message' => 'Yêu cầu chuyển kho đã được hủy.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error cancelling transfer request: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get products with inventory for a warehouse
     */
    public function getWarehouseProducts(Request $request, $warehouseId)
    {
        try {
            $products = DB::table('inventory_items as ii')
                ->join('products as p', 'ii.product_id', '=', 'p.id')
                ->where('ii.warehouse_id', $warehouseId)
                ->where('ii.available_quantity', '>', 0)
                ->where('p.is_active', true)
                ->select(
                    'p.id',
                    'p.code',
                    'p.name',
                    'p.unit',
                    'ii.available_quantity'
                )
                ->get();

            return response()->json([
                'success' => true,
                'data' => $products
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting warehouse products: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tải danh sách sản phẩm.'
            ], 500);
        }
    }
}
