@extends('layouts/layoutMaster')

@section('title', '<PERSON>ế<PERSON> chuyển hàng')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/moment/moment.js',
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/cleavejs/cleave.js',
  'resources/assets/vendor/libs/cleavejs/cleave-phone.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('page-script')
@vite(['resources/js/pages/transfers/transfer-orders.js'])
@endsection

@section('content')
<div class="row g-6 mb-6">
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-start justify-content-between">
          <div class="content-left">
            <span class="text-heading">Tổng phiếu chuyển</span>
            <div class="d-flex align-items-center my-1">
              <h4 class="mb-0 me-2" id="total-orders">0</h4>
            </div>
          </div>
          <div class="avatar">
            <span class="avatar-initial rounded bg-label-primary">
              <i class="ri-truck-line ri-24px"></i>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-start justify-content-between">
          <div class="content-left">
            <span class="text-heading">Chờ duyệt</span>
            <div class="d-flex align-items-center my-1">
              <h4 class="mb-0 me-2" id="pending-orders">0</h4>
            </div>
          </div>
          <div class="avatar">
            <span class="avatar-initial rounded bg-label-warning">
              <i class="ri-time-line ri-24px"></i>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-start justify-content-between">
          <div class="content-left">
            <span class="text-heading">Đã duyệt</span>
            <div class="d-flex align-items-center my-1">
              <h4 class="mb-0 me-2" id="approved-orders">0</h4>
            </div>
          </div>
          <div class="avatar">
            <span class="avatar-initial rounded bg-label-success">
              <i class="ri-check-line ri-24px"></i>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-start justify-content-between">
          <div class="content-left">
            <span class="text-heading">Từ chối</span>
            <div class="d-flex align-items-center my-1">
              <h4 class="mb-0 me-2" id="rejected-orders">0</h4>
            </div>
          </div>
          <div class="avatar">
            <span class="avatar-initial rounded bg-label-danger">
              <i class="ri-close-line ri-24px"></i>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Transfer Orders List Table -->
<div class="card">
  <div class="card-header border-bottom">
    <h5 class="card-title mb-0">Danh sách phiếu chuyển hàng</h5>
    <div class="d-flex justify-content-between align-items-center row pt-4 gap-4 gap-md-0">
      <div class="col-md-4 col-12">
        <select id="status-filter" class="form-select text-capitalize">
          <option value="">Tất cả trạng thái</option>
          <option value="draft">Nháp</option>
          <option value="pending">Chờ duyệt</option>
          <option value="approved">Đã duyệt</option>
          <option value="rejected">Từ chối</option>
          <option value="cancelled">Đã hủy</option>
        </select>
      </div>
      <div class="col-md-4 col-12">
        <select id="from-warehouse-filter" class="form-select">
          <option value="">Tất cả kho nguồn</option>
          @foreach($warehouses as $warehouse)
            <option value="{{ $warehouse->id }}">{{ $warehouse->name }}</option>
          @endforeach
        </select>
      </div>
      <div class="col-md-4 col-12">
        <select id="to-warehouse-filter" class="form-select">
          <option value="">Tất cả kho đích</option>
          @foreach($warehouses as $warehouse)
            <option value="{{ $warehouse->id }}">{{ $warehouse->name }}</option>
          @endforeach
        </select>
      </div>
    </div>
  </div>
  <div class="card-datatable table-responsive">
    <table class="datatables-transfer-orders table">
      <thead class="table-light">
        <tr>
          <th></th>
          <th>Mã phiếu chuyển</th>
          <th>Mã yêu cầu</th>
          <th>Kho nguồn</th>
          <th>Kho đích</th>
          <th>Kho trung gian</th>
          <th>Trạng thái</th>
          <th>Người tạo</th>
          <th>Ngày tạo</th>
          <th>Thao tác</th>
        </tr>
      </thead>
    </table>
  </div>
</div>

@can('transfer-orders.create')
<!-- Add Transfer Order Button -->
<div class="buy-now">
  <a href="{{ route('transfer-orders.create') }}" class="btn btn-primary btn-buy-now">
    <i class="ri-add-line ri-16px me-2"></i>
    Tạo phiếu chuyển hàng
  </a>
</div>
@endcan

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Từ chối phiếu chuyển hàng</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="rejectForm">
        <div class="modal-body">
          <div class="mb-3">
            <label for="reject-reason" class="form-label">Lý do từ chối <span class="text-danger">*</span></label>
            <textarea class="form-control" id="reject-reason" name="reason" rows="3" placeholder="Nhập lý do từ chối..." required></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Hủy</button>
          <button type="submit" class="btn btn-danger">Từ chối</button>
        </div>
      </form>
    </div>
  </div>
</div>
@endsection
