@extends('layouts/layoutMaster')

@section('title', 'Chi tiết phiếu nhập kho')

@section('vendor-style')
  @vite([
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
  ])
@endsection

@section('vendor-script')
  @vite([
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js',
  'resources/assets/vendor/libs/xlsx/xlsx.full.min.js'
  ])
@endsection

@section('page-script')
  @vite([
    'resources/js/pages/warehouses/good-receipt-show.js',
    'resources/js/pages/warehouses/good-receipt-invoice.js',
    'resources/js/pages/warehouses/good-receipt-approval.js',
    'resources/js/pages/warehouses/good-receipt-import.js',
    'resources/js/pages/warehouses/good-receipt-save-draft.js',
    'resources/js/pages/warehouses/good-receipt-attachments.js',
    'resources/js/pages/warehouses/good-receipt-invoices.js',
    'resources/js/pages/warehouses/good-receipt-view-batches.js'
  ])
  <style>

  </style>
@endsection

@section('content')
  <h4 class="mb-1">Chi tiết phiếu nhập kho</h4>
  <p class="mb-6">Xem thông tin chi tiết phiếu nhập kho</p>

  <div class="row">
    <div class="col-12" id="good-receipt-container" data-good-receipt-status="{{ $goodReceipt->status }}" data-good-receipt-id="{{ $goodReceipt->id }}">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">Thông tin phiếu nhập kho</h5>
          <div class="d-flex">
            <a href="{{ route('warehouses.good-receipts.index') }}" class="btn btn-secondary btn-sm">
              <i class="ri-arrow-left-line me-1"></i>Quay lại
            </a>
          </div>
        </div>
        <div class="card-body">
          <div class="row mb-4">
            <div class="col-md-4">
              <div class="mb-3">
                <h6 class="fw-semibold">Mã phiếu:</h6>
                <p>{{ $goodReceipt->code }}</p>
              </div>
              <div class="mb-3">
                <h6 class="fw-semibold">Kho hàng:</h6>
                <p>{{ $goodReceipt->warehouse->name }}</p>
              </div>
              @if($goodReceipt->purchaseOrder)
                <div class="mb-3">
                  <h6 class="fw-semibold">Phiếu đặt hàng:</h6>
                  <p>
                    <a href="{{ route('warehouses.purchase-orders.show', $goodReceipt->purchaseOrder->id) }}">
                      {{ $goodReceipt->purchaseOrder->code }}
                    </a>
                  </p>
                </div>
              @endif
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <h6 class="fw-semibold">Ngày tạo:</h6>
                <p>{{ $goodReceipt->created_at->format('d/m/Y H:i') }}</p>
              </div>
              <div class="mb-3">
                <h6 class="fw-semibold">Người tạo:</h6>
                <p>{{ $goodReceipt->createdBy->name ?? 'N/A' }}</p>
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <h6 class="fw-semibold">Trạng thái:</h6>
                <p>
                  <span class="badge bg-label-{{ $goodReceipt->status_color }} badge-status">
                    {{ $goodReceipt->status_text }}
                  </span>
                </p>
              </div>
            </div>
          </div>

          @if($goodReceipt->notes)
            <div class="row mb-4">
              <div class="col-12">
                <h6 class="fw-semibold">Ghi chú:</h6>
                <p>{{ $goodReceipt->notes }}</p>
              </div>
            </div>
          @endif

          <div class="row">
            <div class="col-12">
              <div class="action-buttons">
                @if($goodReceipt->status === \App\Models\GoodReceipt::STATUS_DRAFT)
                  <button type="button" class="btn btn-success" id="saveDraftBtn" data-id="{{ $goodReceipt->id }}">
                    <i class="ri-save-line me-1"></i>Lưu
                  </button>

                  <button type="button" class="btn btn-primary" id="submitBtn" data-id="{{ $goodReceipt->id }}">
                    <i class="ri-check-line me-1"></i>Yêu cầu duyệt
                  </button>

                  <button type="button" class="btn btn-info" id="updateInvoiceBtn" data-id="{{ $goodReceipt->id }}">
                    <i class="ri-file-list-3-line me-1"></i>Nhập hóa đơn
                  </button>
                @endif

                @if($goodReceipt->status === \App\Models\GoodReceipt::STATUS_PENDING)
                  <button type="button" class="btn btn-success" id="approveBtn" data-id="{{ $goodReceipt->id }}">
                    <i class="ri-check-double-line me-1"></i>Duyệt phiếu
                  </button>
                @endif

                @if($goodReceipt->canBeCancelled())
                  <button type="button" class="btn btn-outline-danger" id="cancelBtn" data-id="{{ $goodReceipt->id }}">
                    <i class="ri-delete-bin-line me-1"></i>Hủy phiếu
                  </button>
                @endif

                <a href="{{ route('warehouses.good-receipts.print', $goodReceipt->id) }}" class="btn btn-outline-primary" target="_blank">
                  <i class="ri-printer-line me-1"></i>In phiếu
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">Danh sách sản phẩm</h5>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-bordered table-products">
              <thead>
                <tr>
                  <th>#</th>
                  <th>SKU</th>
                  <th>Sản phẩm</th>
                  <th>Đơn vị</th>
                  <th>Số lượng</th>
                  <th>Đơn giá (trước VAT)</th>
                  <th>Đơn giá (sau VAT)</th>
                  <th>Giảm giá</th>
                  <th>Thành tiền</th>
                  <th>Khu vực</th>
                  <th>Trạng thái</th>
                </tr>
              </thead>
              <tbody>
                @foreach($goodReceipt->items as $index => $item)
                @php
                    $isImported = false;

                    // Nếu phiếu đã được duyệt hoặc đang chờ duyệt, tất cả sản phẩm đều đã được nhập
                    if ($goodReceipt->status === \App\Models\GoodReceipt::STATUS_COMPLETED || $goodReceipt->status === \App\Models\GoodReceipt::STATUS_PENDING) {
                        $isImported = true;
                    } else {
                    // Kiểm tra trạng thái nhập hàng dựa trên loại sản phẩm
                    if ($item->product->inventory_tracking_type === 'serial') {
                        // Kiểm tra số lượng serial đã nhập
                        $isImported = $item->serials->count() > 0;
                    } elseif ($item->product->inventory_tracking_type === 'batch') {
                        // Kiểm tra số lượng batch đã nhập
                        $isImported = $item->batches->count() > 0 || !empty($item->warehouse_area_id);
                    } elseif ($item->product->inventory_tracking_type === 'quantity') {
                        // Đối với sản phẩm loại quantity, kiểm tra xem warehouse_area_id đã được cập nhật chưa
                        $isImported = !empty($item->warehouse_area_id);
                    }
                }
                @endphp
                  <tr data-item-id="{{ $item->id }}" data-product-id="{{ $item->product_id }}" data-is-imported="{{ $isImported ? 'true' : 'false' }}" data-tracking-type="{{ $item->product->inventory_tracking_type }}" data-quantity="{{ $item->quantity }}">
                    <td>{{ $index + 1 }}</td>
                    <td>{{ $item->product->code }}</td>
                    <td>
                      <div class="d-flex flex-column">
                        <span class="fw-medium product-name" title="{{ $item->product->name }}">{{ $item->product->name }}</span>
                        @if($item->product->isTrackedBySerial())
                          <div class="mt-1">
                            <button type="button" class="btn btn-sm btn-outline-primary view-serials-btn" data-id="{{ $item->id }}">
                              <i class="ri-barcode-line me-1"></i>Xem IMEI
                            </button>
                          </div>
                        @endif
                        @if($item->product->inventory_tracking_type === 'batch')
                          <div class="mt-1">
                            <button type="button" class="btn btn-sm btn-outline-info view-batches-btn" data-id="{{ $item->id }}" data-product-name="{{ $item->product->name }}">
                              <i class="ri-list-check-2 me-1"></i>Xem Batch
                            </button>
                          </div>
                        @endif
                      </div>
                    </td>
                    <td>{{ $item->product->unit ?? 'N/A' }}</td>
                    <td class="text-end"><span class="d-inline-block text-truncate" title="{{ number_format($item->quantity, 0, ',', '.') }}">{{ number_format($item->quantity, 0, ',', '.') }}</span></td>
                    <td class="text-end"><span class="d-inline-block text-truncate" title="{{ number_format($item->unit_price, 0, ',', '.') }} đ">{{ number_format($item->unit_price, 0, ',', '.') }} đ</span></td>
                    <td class="text-end"><span class="d-inline-block text-truncate" title="{{ number_format($item->unit_price_after_tax ?? ($item->unit_price * (1 + ($item->tax_rate ?? 0) / 100)), 0, ',', '.') }} đ">{{ number_format($item->unit_price_after_tax ?? ($item->unit_price * (1 + ($item->tax_rate ?? 0) / 100)), 0, ',', '.') }} đ</span></td>
                    <td class="text-end"><span class="d-inline-block text-truncate" title="{{ number_format($item->discount ?? 0, 0, ',', '.') }} đ">{{ number_format($item->discount ?? 0, 0, ',', '.') }} đ</span></td>
                    <td class="text-end"><span class="d-inline-block text-truncate" title="{{ number_format($item->subtotal, 0, ',', '.') }} đ">{{ number_format($item->subtotal, 0, ',', '.') }} đ</span></td>
                    <td>
                      <span title="{{ $item->warehouseArea ? $item->warehouseArea->name : 'N/A' }}">{{ $item->warehouseArea ? $item->warehouseArea->name : 'N/A' }}</span>
                    </td>
                    <td>
                      <span class="badge {{ $isImported ? 'bg-label-success' : 'bg-label-secondary' }} item-status" title="Trạng thái nhập hàng">{{ $isImported ? 'Đã nhập' : 'Chưa nhập' }}</span>
                      @if($goodReceipt->status === \App\Models\GoodReceipt::STATUS_DRAFT && !$isImported)
                        <button type="button" class="btn btn-sm btn-primary mt-1 import-item-btn">
                          <i class="ri-download-2-line me-1"></i>Nhập hàng
                        </button>
                      @endif
                    </td>
                  </tr>
                @endforeach
              </tbody>
              <tfoot>
                <tr>
                  <td colspan="8" class="text-end fw-bold">Tổng tiền hàng (trước VAT):</td>
                  <td colspan="3" class="text-end fw-bold">{{ number_format($goodReceipt->items->sum(function($item) { return $item->quantity * $item->unit_price; }), 0, ',', '.') }} đ</td>
                </tr>
                <tr>
                  <td colspan="8" class="text-end fw-bold">Tổng tiền VAT:</td>
                  <td colspan="3" class="text-end fw-bold">{{ number_format($goodReceipt->items->sum(function($item) { return $item->quantity * $item->unit_price * (($item->tax_rate ?? 0) / 100); }), 0, ',', '.') }} đ</td>
                </tr>
                <tr>
                  <td colspan="8" class="text-end fw-bold">Tổng tiền hàng (sau VAT):</td>
                  <td colspan="3" class="text-end fw-bold">{{ number_format($goodReceipt->items->sum(function($item) { return $item->quantity * $item->unit_price * (1 + ($item->tax_rate ?? 0) / 100); }), 0, ',', '.') }} đ</td>
                </tr>
                <tr>
                  <td colspan="8" class="text-end fw-bold">Tổng giảm giá:</td>
                  <td colspan="3" class="text-end fw-bold">{{ number_format($goodReceipt->items->sum(function($item) { return $item->quantity * ($item->discount ?? 0); }), 0, ',', '.') }} đ</td>
                </tr>
                <tr>
                  <td colspan="8" class="text-end fw-bold">Tổng tiền phải trả:</td>
                  <td colspan="3" class="text-end fw-bold">{{ number_format($goodReceipt->total_amount, 0, ',', '.') }} đ</td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      </div>
    </div>

    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">Danh sách hóa đơn</h5>
          @if($goodReceipt->status === \App\Models\GoodReceipt::STATUS_DRAFT || $goodReceipt->status === \App\Models\GoodReceipt::STATUS_PENDING)
            <button type="button" class="btn btn-sm btn-primary" id="addInvoiceBtn" data-id="{{ $goodReceipt->id }}">
              <i class="ri-add-line me-1"></i>Thêm hóa đơn
            </button>
          @endif
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-bordered" id="invoicesTable">
              <thead>
                <tr>
                  <th style="width: 5%">#</th>
                  <th style="width: 25%">Số hóa đơn</th>
                  <th style="width: 20%">Ngày hóa đơn</th>
                  <th style="width: 30%">File đính kèm</th>
                  <th style="width: 20%">Thao tác</th>
                </tr>
              </thead>
              <tbody>
                <tr id="loadingInvoicesRow">
                  <td colspan="5" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Đang tải danh sách hóa đơn...</p>
                  </td>
                </tr>
                <tr id="noInvoicesRow" style="display: none;">
                  <td colspan="5" class="text-center">Không có hóa đơn nào</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">File đính kèm khác</h5>
          @if($goodReceipt->status === \App\Models\GoodReceipt::STATUS_DRAFT || $goodReceipt->status === \App\Models\GoodReceipt::STATUS_PENDING)
            <button type="button" class="btn btn-sm btn-primary" id="uploadAttachmentBtn" data-id="{{ $goodReceipt->id }}">
              <i class="ri-upload-2-line me-1"></i>Tải lên file đính kèm
            </button>
          @endif
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-bordered" id="attachmentsTable">
              <thead>
                <tr>
                  <th style="width: 5%">#</th>
                  <th style="width: 40%">Tên file</th>
                  <th style="width: 15%">Loại file</th>
                  <th style="width: 15%">Kích thước</th>
                  <th style="width: 15%">Ngày tải lên</th>
                  <th style="width: 10%">Thao tác</th>
                </tr>
              </thead>
              <tbody>
                <tr id="loadingAttachmentsRow">
                  <td colspan="6" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Đang tải danh sách file đính kèm...</p>
                  </td>
                </tr>
                <tr id="noAttachmentsRow" style="display: none;">
                  <td colspan="6" class="text-center">Không có file đính kèm nào</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title mb-0">Trạng thái phiếu</h5>
        </div>
        <div class="card-body">
          @if($goodReceipt->status === \App\Models\GoodReceipt::STATUS_DRAFT)
            <div class="alert alert-secondary mb-0">
              <h6 class="alert-heading mb-1"><i class="ri-information-line me-1"></i>Thông tin</h6>
              <p class="mb-0">Phiếu nhập kho đang ở trạng thái nháp. Bạn cần nhập IMEI/số lô/số lượng và thông tin hóa đơn trước khi gửi yêu cầu duyệt.</p>
            </div>
          @elseif($goodReceipt->status === \App\Models\GoodReceipt::STATUS_PENDING)
            <div class="alert alert-warning mb-0">
              <h6 class="alert-heading mb-1"><i class="ri-information-line me-1"></i>Thông tin</h6>
              <p class="mb-0">Phiếu nhập kho đang chờ duyệt. Sau khi duyệt, số lượng sản phẩm sẽ được cập nhật vào tồn kho.</p>
            </div>
          @elseif($goodReceipt->status === \App\Models\GoodReceipt::STATUS_COMPLETED)
            <div class="alert alert-success mb-0">
              <h6 class="alert-heading mb-1"><i class="ri-information-line me-1"></i>Thông tin</h6>
              <p class="mb-0">Phiếu nhập kho đã được duyệt và cập nhật vào hệ thống. Số lượng sản phẩm đã được cập nhật vào tồn kho.</p>
            </div>
          @elseif($goodReceipt->status === \App\Models\GoodReceipt::STATUS_CANCELLED)
            <div class="alert alert-danger mb-0">
              <h6 class="alert-heading mb-1"><i class="ri-information-line me-1"></i>Thông tin</h6>
              <p class="mb-0">Phiếu nhập kho đã bị hủy.</p>
            </div>
          @endif
        </div>
      </div>
    </div>
  </div>

  <!-- Cancel Modal -->
  <div class="modal fade" id="cancelModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Hủy phiếu nhập kho</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="cancelForm">
            <input type="hidden" id="cancel_id" name="cancel_id">
            <div class="mb-3">
              <label for="cancel_reason" class="form-label">Lý do hủy</label>
              <textarea id="cancel_reason" name="reason" class="form-control" rows="3" required></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">Đóng</button>
          <button type="button" class="btn btn-danger" id="confirmCancelBtn">Hủy phiếu</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Serials Modal -->
  <div class="modal fade" id="serialsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Danh sách IMEI/Serial</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="table-responsive">
            <table class="table table-bordered" id="serialsTable">
              <thead>
                <tr>
                  <th style="width: 5%">#</th>
                  <th style="width: 40%">IMEI/Serial</th>
                  <th style="width: 30%">Trạng thái</th>
                  <th style="width: 25%">Ngày nhập</th>
                </tr>
              </thead>
              <tbody>
                <tr id="loadingRow">
                  <td colspan="4" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Đang tải danh sách IMEI/Serial...</p>
                  </td>
                </tr>
                <tr id="noSerialsRow" style="display: none;">
                  <td colspan="4" class="text-center">Không có IMEI/Serial nào</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">Đóng</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Scan IMEI Modal -->
  <div class="modal fade" id="scanImeiModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Nhập IMEI/Serial</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row mb-3">
            <div class="col-12">
              <div class="alert alert-info">
                <h6 class="alert-heading mb-1"><i class="ri-information-line me-1"></i>Hướng dẫn</h6>
                <p class="mb-0">Nhập IMEI/Serial cho sản phẩm. Mỗi IMEI/Serial phải là duy nhất và chưa tồn tại trong hệ thống.</p>
              </div>
            </div>
          </div>

          <div class="row mb-3">
            <div class="col-12">
              <div class="input-group">
                <input type="text" class="form-control" id="imeiInput" placeholder="Nhập IMEI/Serial">
                <button class="btn btn-primary" type="button" id="scanImeiBtn">Thêm</button>
              </div>
              <div class="form-text">Nhập IMEI/Serial và nhấn Enter hoặc nút Thêm để quét.</div>
            </div>
          </div>

          <div class="row mb-3">
            <div class="col-12">
              <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0">Danh sách IMEI/Serial đã quét</h6>
                <span class="badge bg-primary" id="scannedCount">0</span>
              </div>
              <div class="table-responsive">
                <table class="table table-bordered" id="scannedImeiTable">
                  <thead>
                    <tr>
                      <th style="width: 5%">#</th>
                      <th style="width: 70%">IMEI/Serial</th>
                      <th style="width: 25%">Thao tác</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr id="noScannedImeiRow">
                      <td colspan="3" class="text-center">Chưa có IMEI/Serial nào được quét</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">Đóng</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Add Invoice Modal -->
  <div class="modal fade" id="addInvoiceModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Thêm hóa đơn mới</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="invoiceForm">
            <input type="hidden" id="invoice_id" name="invoice_id" value="">
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="invoice_number" class="form-label">Số hóa đơn <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="invoice_number" name="invoice_number" required>
              </div>
              <div class="col-md-6">
                <label for="invoice_date" class="form-label">Ngày hóa đơn <span class="text-danger">*</span></label>
                <input type="date" class="form-control" id="invoice_date" name="invoice_date" required>
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="invoice_template" class="form-label">Mẫu số</label>
                <input type="text" class="form-control" id="invoice_template" name="invoice_template">
              </div>
              <div class="col-md-6">
                <label for="invoice_series" class="form-label">Số seri</label>
                <input type="text" class="form-control" id="invoice_series" name="invoice_series">
              </div>
            </div>

            <div class="row mb-3">
              <div class="col-12">
                <label for="invoice_notes" class="form-label">Ghi chú</label>
                <textarea class="form-control" id="invoice_notes" name="notes" rows="2"></textarea>
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-12">
                <label for="invoice_files" class="form-label">File hóa đơn</label>
                <input type="file" class="form-control" id="invoice_files" name="invoice_files[]" accept=".jpg,.jpeg,.png,.pdf,.doc,.docx" multiple>
                <div class="form-text">Chấp nhận các định dạng: JPG, PNG, PDF, DOC, DOCX. Kích thước tối đa: 10MB. Có thể chọn nhiều file.</div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">Đóng</button>
          <button type="button" class="btn btn-primary" id="saveInvoiceBtn">Lưu</button>
        </div>
      </div>
    </div>
  </div>

  <!-- View Invoice Attachments Modal -->
  <div class="modal fade" id="viewInvoiceAttachmentsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">File đính kèm hóa đơn</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="table-responsive">
            <table class="table table-bordered" id="invoiceAttachmentsTable">
              <thead>
                <tr>
                  <th style="width: 5%">#</th>
                  <th style="width: 40%">Tên file</th>
                  <th style="width: 15%">Loại file</th>
                  <th style="width: 15%">Kích thước</th>
                  <th style="width: 15%">Ngày tải lên</th>
                  <th style="width: 10%">Thao tác</th>
                </tr>
              </thead>
              <tbody>
                <tr id="loadingInvoiceAttachmentsRow">
                  <td colspan="6" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Đang tải danh sách file đính kèm...</p>
                  </td>
                </tr>
                <tr id="noInvoiceAttachmentsRow" style="display: none;">
                  <td colspan="6" class="text-center">Không có file đính kèm nào</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">Đóng</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Import Quantity Modal -->
  <div class="modal fade" id="importQuantityModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Nhập hàng theo số lượng</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="importQuantityForm">
            <input type="hidden" id="quantity_item_id" name="item_id">
            <input type="hidden" id="quantity_product_id" name="product_id">
            <div class="row mb-3">
              <div class="col-12">
                <div class="alert alert-info">
                  <h6 class="alert-heading mb-1"><i class="ri-information-line me-1"></i>Thông tin sản phẩm</h6>
                  <p class="mb-0" id="quantity_product_info">Tên sản phẩm: <span class="fw-medium"></span></p>
                  <p class="mb-0">Số lượng cần nhập: <span class="fw-medium" id="quantity_required"></span></p>
                </div>
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-12">
                <label for="quantity_value" class="form-label">Số lượng nhập <span class="text-danger">*</span></label>
                <input type="number" class="form-control" id="quantity_value" name="quantity" min="1" required>
                <div class="invalid-feedback">Số lượng không hợp lệ</div>
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-12">
                <label for="quantity_warehouse_area" class="form-label">Vị trí kho <span class="text-danger">*</span></label>
                <select class="form-select select2" id="quantity_warehouse_area" name="warehouse_area_id" required>
                  <option value="">Chọn vị trí kho</option>
                  @foreach($goodReceipt->warehouse->areas as $area)
                    <option value="{{ $area->id }}">{{ $area->name }}</option>
                  @endforeach
                </select>
                <div class="invalid-feedback">Vui lòng chọn vị trí kho</div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">Đóng</button>
          <button type="button" class="btn btn-primary" id="saveQuantityBtn">Lưu</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Import Batch Modal -->
  <div class="modal fade" id="importBatchModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Nhập hàng theo lô</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="importBatchForm">
            <input type="hidden" id="batch_item_id" name="item_id">
            <input type="hidden" id="batch_product_id" name="product_id">
            <div class="row mb-3">
              <div class="col-12">
                <div class="alert alert-info">
                  <h6 class="alert-heading mb-1"><i class="ri-information-line me-1"></i>Thông tin sản phẩm</h6>
                  <p class="mb-0" id="batch_product_info">Tên sản phẩm: <span class="fw-medium"></span></p>
                  <p class="mb-0">Số lượng cần nhập: <span class="fw-medium" id="batch_required"></span></p>
                  <p class="mb-0">Số lô đã nhập: <span class="fw-medium" id="batch_scanned">0</span></p>
                  <p class="mb-0">Tổng số lượng đã nhập: <span class="fw-medium" id="batch_total_quantity">0</span></p>
                  <p class="mb-0">Số lượng còn lại: <span class="fw-medium" id="batch_remaining_quantity">0</span></p>
                </div>
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-12">
                <label for="batch_warehouse_area" class="form-label">Vị trí kho <span class="text-danger">*</span></label>
                <select class="form-select select2" id="batch_warehouse_area" name="warehouse_area_id" required>
                  <option value="">Chọn vị trí kho</option>
                  @foreach($goodReceipt->warehouse->areas as $area)
                    <option value="{{ $area->id }}">{{ $area->name }}</option>
                  @endforeach
                </select>
                <div class="invalid-feedback">Vui lòng chọn vị trí kho</div>
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="batch_number_input" class="form-label">Số lô</label>
                <div class="input-group">
                  <input type="text" class="form-control" id="batch_number_input" name="batch_number_input" placeholder="Nhập số lô">
                  <button class="btn btn-primary" type="button" id="addBatchBtn">Thêm</button>
                </div>
                <div class="form-text">Nhập số lô và nhấn Enter hoặc nút Thêm để thêm vào danh sách.</div>
              </div>
              <div class="col-md-6">
                <label for="batch_item_quantity" class="form-label">Số lượng cho lô này</label>
                <input type="number" class="form-control" id="batch_item_quantity" name="batch_item_quantity" min="1" placeholder="Nhập số lượng cho lô này">
                <div class="form-text">Nếu để trống, số lượng sẽ được phân bổ tự động.</div>
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="manufacturing_date" class="form-label">Ngày sản xuất</label>
                <input type="date" class="form-control" id="manufacturing_date" name="manufacturing_date">
              </div>
              <div class="col-md-6">
                <label for="batch_expiry_date" class="form-label">Ngày hết hạn <span class="text-danger">*</span></label>
                <input type="date" class="form-control" id="batch_expiry_date" name="expiry_date" required>
                <div class="invalid-feedback">Vui lòng chọn ngày hết hạn</div>
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-12">
                <label for="batch_quantity" class="form-label">Số lượng nhập <span class="text-danger">*</span></label>
                <input type="number" class="form-control" id="batch_quantity" name="quantity" min="1" required>
                <div class="invalid-feedback">Số lượng không hợp lệ</div>
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <h6 class="mb-0">Danh sách số lô đã nhập</h6>
                  <span class="badge bg-primary" id="batchCount">0</span>
                </div>
                <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                  <table class="table table-bordered" id="batchTable">
                    <thead>
                      <tr>
                        <th style="width: 5%">#</th>
                        <th style="width: 30%">Số lô</th>
                        <th style="width: 15%">Số lượng</th>
                        <th style="width: 20%">Ngày sản xuất</th>
                        <th style="width: 20%">Ngày hết hạn</th>
                        <th style="width: 10%">Thao tác</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr id="noBatchRow">
                        <td colspan="6" class="text-center">Chưa có số lô nào được nhập</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">Đóng</button>
          <button type="button" class="btn btn-primary" id="saveBatchBtn">Lưu</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Import Serial Modal -->
  <div class="modal fade" id="importSerialModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Nhập hàng theo IMEI/Serial</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="importSerialForm">
            <input type="hidden" id="serial_item_id" name="item_id">
            <input type="hidden" id="serial_product_id" name="product_id">
            <div class="row mb-3">
              <div class="col-12">
                <div class="alert alert-info">
                  <h6 class="alert-heading mb-1"><i class="ri-information-line me-1"></i>Thông tin sản phẩm</h6>
                  <p class="mb-0" id="serial_product_info">Tên sản phẩm: <span class="fw-medium"></span></p>
                  <p class="mb-0">Số lượng cần nhập: <span class="fw-medium" id="serial_required"></span></p>
                  <p class="mb-0">Đã nhập: <span class="fw-medium" id="serial_scanned">0</span></p>
                </div>
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-12">
                <label for="serial_warehouse_area" class="form-label">Vị trí kho <span class="text-danger">*</span></label>
                <select class="form-select select2" id="serial_warehouse_area" name="warehouse_area_id" required>
                  <option value="">Chọn vị trí kho</option>
                  @foreach($goodReceipt->warehouse->areas as $area)
                    <option value="{{ $area->id }}">{{ $area->name }}</option>
                  @endforeach
                </select>
                <div class="invalid-feedback">Vui lòng chọn vị trí kho</div>
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-12">
                <label for="serial_number" class="form-label">IMEI/Serial</label>
                <div class="input-group">
                  <input type="text" class="form-control" id="serial_number" name="serial_number" placeholder="Nhập IMEI/Serial">
                  <button class="btn btn-primary" type="button" id="addSerialBtn">Thêm</button>
                </div>
                <div class="form-text">Nhập IMEI/Serial và nhấn Enter hoặc nút Thêm để quét.</div>
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <label class="form-label mb-0">Nhập từ file Excel</label>
                  <a href="#" class="btn btn-sm btn-outline-primary" id="downloadTemplateBtn">
                    <i class="ri-download-line me-1"></i>Tải mẫu
                  </a>
                </div>
                <input type="file" class="form-control" id="serial_file" name="serial_file" accept=".xlsx,.xls">
                <div class="form-text">Chấp nhận các định dạng: XLSX, XLS. Kích thước tối đa: 5MB.</div>
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <h6 class="mb-0">Danh sách IMEI/Serial đã quét</h6>
                  <span class="badge bg-primary" id="serialCount">0</span>
                </div>
                <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                  <table class="table table-bordered" id="serialTable">
                    <thead>
                      <tr>
                        <th style="width: 5%">#</th>
                        <th style="width: 75%">IMEI/Serial</th>
                        <th style="width: 20%">Thao tác</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr id="noSerialRow">
                        <td colspan="3" class="text-center">Chưa có IMEI/Serial nào được quét</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">Đóng</button>
          <button type="button" class="btn btn-primary" id="saveSerialBtn">Lưu</button>
        </div>
      </div>
    </div>
  </div>

  <!-- View Serials Modal -->
  <div class="modal fade" id="viewSerialsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Danh sách IMEI/Serial</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th style="width: 5%">#</th>
                  <th style="width: 95%">IMEI/Serial</th>
                </tr>
              </thead>
              <tbody id="serialsTableBody">
                <tr>
                  <td colspan="2" class="text-center">Không có dữ liệu</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">Đóng</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Upload Attachment Modal -->
  <div class="modal fade" id="uploadAttachmentModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Tải lên file đính kèm</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="attachmentForm">
            <div class="row mb-3">
              <div class="col-12">
                <label for="attachment_file" class="form-label">File đính kèm <span class="text-danger">*</span></label>
                <input type="file" class="form-control" id="attachment_file" name="file" required>
                <div class="form-text">Chấp nhận các định dạng: JPG, PNG, PDF, DOC, DOCX, XLS, XLSX. Kích thước tối đa: 10MB.</div>
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-12">
                <label for="attachment_description" class="form-label">Mô tả</label>
                <input type="text" class="form-control" id="attachment_description" name="description" placeholder="Mô tả ngắn về file đính kèm">
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">Đóng</button>
          <button type="button" class="btn btn-primary" id="saveAttachmentBtn">Tải lên</button>
        </div>
      </div>
    </div>
  </div>

  <!-- View Batches Modal -->
  <div class="modal fade" id="viewBatchesModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Danh sách Batch - <span id="batch-product-name"></span></h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="table-responsive">
            <table class="table table-bordered" id="batchesTable">
              <thead>
                <tr>
                  <th style="width: 5%">#</th>
                  <th style="width: 40%">Số lô</th>
                  <th style="width: 20%">Số lượng</th>
                  <th style="width: 35%">Ngày hết hạn</th>
                </tr>
              </thead>
              <tbody>
                <tr id="loadingBatchesRow">
                  <td colspan="4" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Đang tải danh sách batch...</p>
                  </td>
                </tr>
                <tr id="noBatchesRow" style="display: none;">
                  <td colspan="4" class="text-center">Không có batch nào</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">Đóng</button>
        </div>
      </div>
    </div>
  </div>

  <!-- View Batches Modal -->
  <div class="modal fade" id="viewBatchesModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Danh sách Batch - <span id="batch-product-name"></span></h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="table-responsive">
            <table class="table table-bordered" id="batchesTable">
              <thead>
                <tr>
                  <th style="width: 5%">#</th>
                  <th style="width: 40%">Số lô</th>
                  <th style="width: 20%">Số lượng</th>
                  <th style="width: 35%">Ngày hết hạn</th>
                </tr>
              </thead>
              <tbody>
                <tr id="loadingBatchesRow">
                  <td colspan="4" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Đang tải danh sách batch...</p>
                  </td>
                </tr>
                <tr id="noBatchesRow" style="display: none;">
                  <td colspan="4" class="text-center">Không có batch nào</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">Đóng</button>
        </div>
      </div>
    </div>
  </div>
@endsection
