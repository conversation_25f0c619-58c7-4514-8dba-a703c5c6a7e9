@extends('layouts/layoutMaster')

@section('title', 'Quản lý tồn kho')

@section('vendor-style')
  @vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-checkboxes-jquery/datatables.checkboxes.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
  ])
@endsection

@section('vendor-script')
  @vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js',
  'resources/assets/js/ui-popover.js'
  ])
@endsection

@section('page-script')
  @vite('resources/js/pages/warehouses/warehouse-inventory.js')
@endsection

@section('content')
  <h4 class="mb-1">Quản lý tồn kho</h4>
  <p class="mb-6">Quản lý tồn kho trong các kho hàng</p>

  <!-- Filter -->
  <div class="card mb-4">
    <div class="card-body">
      <div class="row">
        <div class="col-md-6 mb-3">
          <label for="warehouse-filter" class="form-label">Lọc theo kho hàng</label>
          <select id="warehouse-filter" class="form-select">
            <option value="">Tất cả kho hàng</option>
            @foreach($warehouses as $warehouse)
              <option value="{{ $warehouse->id }}" {{ $selectedWarehouse && $selectedWarehouse->id == $warehouse->id ? 'selected' : '' }}>{{ $warehouse->name }}</option>
            @endforeach
          </select>
        </div>
      </div>
    </div>
  </div>

  <!-- Inventory Actions -->
  <div class="card mb-4">
    <div class="card-body">
      <div class="row">
        <div class="col-md-12">
          <div class="d-flex flex-wrap gap-3">
            @can('warehouses.inventory.add')
              <a href="{{ route('warehouses.inventory.add') }}" class="btn btn-primary">
                <i class="ri-arrow-down-line me-1"></i>Nhập kho
              </a>
            @endcan
            @can('warehouses.inventory.remove')
              <a href="{{ route('warehouses.inventory.remove') }}" class="btn btn-danger">
                <i class="ri-arrow-up-line me-1"></i>Xuất kho
              </a>
            @endcan
            @can('warehouses.inventory.transfer')
              <a href="{{ route('warehouses.inventory.transfer') }}" class="btn btn-info">
                <i class="ri-arrow-left-right-line me-1"></i>Chuyển kho
              </a>
            @endcan
            @can('warehouses.inventory.adjust')
              <a href="{{ route('warehouses.inventory.adjust') }}" class="btn btn-warning">
                <i class="ri-scales-3-line me-1"></i>Điều chỉnh tồn kho
              </a>
            @endcan
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Inventory List -->
  <div class="card">
    <div class="card-header border-bottom">
      <h5 class="card-title mb-0">Danh sách tồn kho</h5>
    </div>
    <div class="card-datatable table-responsive">
      <table class="datatables-inventory table border-top">
        <thead>
        <tr>
          <th></th>
          <th>Sản phẩm</th>
          <th>Kho hàng</th>
          <th>Tồn kho</th>
          <th>Khả dụng</th>
          <th>Đặt trước</th>
          <th>Mức tối thiểu</th>
          <th>Mức tối đa</th>
          <th>Cập nhật lúc</th>
          <th>Thao tác</th>
        </tr>
        </thead>
      </table>
    </div>
  </div>
@endsection
